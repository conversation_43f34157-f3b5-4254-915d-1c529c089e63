import { Brain, Palette, Heart } from 'lucide-react'

export const features = [
  {
    title: 'AI智能创作',
    description: '通过先进的AI技术，将孩子的想象力转化为精彩的故事',
    icon: Brain,
    image: '/images/features/ai-creation.png',
    benefits: [
      '智能故事生成',
      '个性化内容定制',
      '多样化故事主题',
      '教育价值导向'
    ]
  },
  {
    title: '精美插画生成',
    description: '专业的AI绘画技术，为每个故事创作独特的视觉体验',
    icon: Palette,
    image: '/images/features/illustration.png',
    benefits: [
      '高质量插画生成',
      '场景智能匹配',
      '风格个性定制',
      '实时预览效果'
    ]
  },
  {
    title: '寓教于乐',
    description: '将教育价值巧妙融入有趣的故事情节中',
    icon: Heart,
    image: '/images/features/education.png',
    benefits: [
      '知识点融入',
      '品德教育',
      '趣味性学习',
      '家长反馈系统'
    ]
  }
] 