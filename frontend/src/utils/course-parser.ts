import { CourseContent, CourseSection, CourseStep } from '../types/course';

export function parseCourseContent(markdown: string): CourseContent {
  const lines = markdown.split('\n');
  const content: CourseContent = {
    title: '',
    sections: [],
  };

  let currentSection: CourseSection | null = null;
  let currentStep: CourseStep | null = null;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    if (line.startsWith('# ')) {
      content.title = line.substring(2);
    } else if (line.startsWith('## ')) {
      if (currentSection) {
        content.sections.push(currentSection);
      }
      currentSection = {
        title: line.substring(3),
        steps: [],
      };
    } else if (line.match(/^\[.*\]:/) || line.startsWith('[practice:')) {
      if (currentStep) {
        currentSection?.steps.push(currentStep);
      }
      
      if (line.startsWith('[practice:')) {
        // 处理practice标签
        const practiceMatch = line.match(/\[practice:(\w+)\s+(.+)\]/);
        if (practiceMatch) {
          const [_, practiceType, params] = practiceMatch;
          const practiceParams = parseParams(params);
          currentStep = {
            type: 'ai_practice',
            content: '',
            practice: {
              type: practiceType as 'chat' | 'image',
              mode: practiceParams.mode || 'default',
              character: practiceParams.character,
              temperature: practiceParams.temperature ? parseFloat(practiceParams.temperature) : undefined,
              max_tokens: practiceParams.max_tokens ? parseInt(practiceParams.max_tokens) : undefined,
              system_prompt: practiceParams.system_prompt,
              model: practiceParams.model,
              size: practiceParams.size,
              style: practiceParams.style,
              quality: practiceParams.quality,
              initial_prompt: practiceParams.initial_prompt,
            }
          };
        }
      } else {
        // 处理其他标签
        const match = line.match(/\[(.*)\]:\s*(.*)/);
        if (match) {
          const [_, speaker, content] = match;
          if (content.toLowerCase() === '等待点击') {
            currentStep = {
              type: 'input',
              content: '继续',
              waitForInput: true,
            };
          } else if (content.toLowerCase() === '等待输入') {
            currentStep = {
              type: 'input',
              content: '',
              waitForInput: true,
            };
          } else {
            currentStep = {
              type: 'dialogue',
              speaker,
              content: content || '',
            };
          }
        }
      }
    } else if (line.startsWith('[/practice]')) {
      // 结束practice块
      if (currentStep && currentStep.type === 'ai_practice') {
        currentStep.content = collectPracticeContent(lines, i);
        currentSection?.steps.push(currentStep);
        currentStep = null;
      }
    } else if (currentStep?.type === 'ai_practice') {
      // 收集practice内容
      continue;
    } else if (line && currentStep) {
      currentStep.content += (currentStep.content ? '\n' : '') + line;
    }
  }

  if (currentStep) {
    currentSection?.steps.push(currentStep);
  }
  if (currentSection) {
    content.sections.push(currentSection);
  }

  return content;
}

function parseParams(paramsString: string): Record<string, string> {
  const params: Record<string, string> = {};
  const matches = paramsString.match(/(\w+)="([^"]+)"/g);
  
  if (matches) {
    matches.forEach(match => {
      const [key, value] = match.split('=');
      params[key] = value.replace(/"/g, '');
    });
  }
  
  return params;
}

function collectPracticeContent(lines: string[], startIndex: number): string {
  let content = '';
  let i = startIndex - 1;
  
  while (i >= 0 && !lines[i].trim().startsWith('[practice:')) {
    content = lines[i].trim() + '\n' + content;
    i--;
  }
  
  return content.trim();
} 