'use client'

import { useCallback, useEffect, useRef, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAuth } from '../contexts/simplified-auth-context'

export function useAuthNavigation() {
  const router = useRouter()
  const pathname = usePathname()
  const { isAuthenticated, isLoading: authIsLoading, user } = useAuth()
  const navigationInProgressRef = useRef(false)
  const navigationTimeoutRef = useRef<NodeJS.Timeout>()
  const [isLoading, setIsLoading] = useState(false)

  // 重置导航状态
  const resetNavigation = useCallback(() => {
    navigationInProgressRef.current = false
    setIsLoading(false)
    if (navigationTimeoutRef.current) {
      clearTimeout(navigationTimeoutRef.current)
      navigationTimeoutRef.current = undefined
    }
  }, [])

  // 清理函数
  useEffect(() => {
    return () => {
      resetNavigation()
    }
  }, [resetNavigation])

  // 认证后导航
  const navigateAfterAuth = useCallback(async (targetPath: string) => {
    if (navigationInProgressRef.current) {
      console.log('[useAuthNavigation] 导航正在进行中，跳过', {
        targetPath,
        timestamp: new Date().toISOString()
      })
      return
    }

    try {
      navigationInProgressRef.current = true
      setIsLoading(true)

      console.log('[useAuthNavigation] 开始导航', {
        targetPath,
        isAuthenticated,
        timestamp: new Date().toISOString()
      })

      // 等待认证状态稳定
      if (authIsLoading) {
        console.log('[useAuthNavigation] 等待认证状态稳定', {
          timestamp: new Date().toISOString()
        })
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      // 检查用户角色
      if (user?.role !== 'admin') {
        console.warn('[useAuthNavigation] 用户没有管理员权限', {
          userRole: user?.role,
          timestamp: new Date().toISOString()
        })
        router.push('/admin/login')
        return
      }

      // 执行导航
      router.push(targetPath)
    } catch (error) {
      console.error('[useAuthNavigation] 导航失败', {
        error,
        targetPath,
        timestamp: new Date().toISOString()
      })
      router.push('/admin/login')
    } finally {
      navigationInProgressRef.current = false
      setIsLoading(false)
    }
  }, [router, isAuthenticated, authIsLoading, user])

  // 导航到登录页
  const navigateToLogin = useCallback(() => {
    router.push('/admin/login')
  }, [router])

  return {
    navigateAfterAuth,
    navigateToLogin,
    isLoading
  }
}