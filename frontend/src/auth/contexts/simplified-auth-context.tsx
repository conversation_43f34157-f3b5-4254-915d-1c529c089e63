'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { User } from '@/lib/services/user.service'
import { authApi } from '../services/simplified-auth-api'
import { tokenManager } from '../core/simplified-token-manager'

// 简化的认证状态类型
export type AuthState = {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  error: Error | null
}

// 认证上下文类型
interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<void>
  adminLogin: (email: string, password: string) => Promise<void>
  logout: () => Promise<void>
  register: (data: { email: string; password: string; name: string }) => Promise<void>
  clearError: () => void
}

// 创建认证上下文
const AuthContext = createContext<AuthContextType | null>(null)

// 认证提供者组件
export function AuthProvider({ children }: { children: React.ReactNode }) {
  // 基础状态
  const [state, setState] = useState<AuthState>({
    user: null,
    isLoading: true,
    isAuthenticated: false,
    error: null
  })

  // 初始化认证状态
  useEffect(() => {
    let mounted = true

    const initAuth = async () => {
      try {
        // 检查token是否存在
        const token = tokenManager.getToken()
        if (!token) {
          if (mounted) {
            setState({
              user: null,
              isLoading: false,
              isAuthenticated: false,
              error: null
            })
          }
          return
        }

        // 尝试获取当前用户信息
        try {
          const user = await authApi.getCurrentUser()
          if (mounted) {
            if (user) {
              tokenManager.setUser(user)
              setState({
                user,
                isLoading: false,
                isAuthenticated: true,
                error: null
              })
            } else {
              tokenManager.clear()
              setState({
                user: null,
                isLoading: false,
                isAuthenticated: false,
                error: null
              })
            }
          }
        } catch (error) {
          // API调用失败，可能是token过期
          console.error('获取用户信息失败:', error)
          tokenManager.clear()
          if (mounted) {
            setState({
              user: null,
              isLoading: false,
              isAuthenticated: false,
              error: null
            })
          }
        }
      } catch (error) {
        console.error('认证初始化失败:', error)
        tokenManager.clear()
        if (mounted) {
          setState({
            user: null,
            isLoading: false,
            isAuthenticated: false,
            error: null
          })
        }
      }
    }

    initAuth()

    return () => {
      mounted = false
    }
  }, [])

  // 登录方法
  const login = async (email: string, password: string) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }))
    try {
      const { user, token } = await authApi.login({ email, password })
      tokenManager.setUser(user)
      tokenManager.setToken(token)
      setState({
        user,
        isLoading: false,
        isAuthenticated: true,
        error: null
      })
    } catch (error) {
      console.error('登录失败:', error)
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error : new Error('登录失败')
      }))
      throw error
    }
  }

  // 管理员登录方法
  const adminLogin = async (email: string, password: string) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }))
    try {
      const { user, token } = await authApi.adminLogin({ email, password })
      tokenManager.setUser(user)
      tokenManager.setToken(token)
      setState({
        user,
        isLoading: false,
        isAuthenticated: true,
        error: null
      })
    } catch (error) {
      console.error('管理员登录失败:', error)
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error : new Error('管理员登录失败')
      }))
      throw error
    }
  }

  // 注册方法
  const register = async (data: { email: string; password: string; name: string }) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }))
    try {
      const { user, token } = await authApi.register(data)
      tokenManager.setUser(user)
      tokenManager.setToken(token)
      setState({
        user,
        isLoading: false,
        isAuthenticated: true,
        error: null
      })
    } catch (error) {
      console.error('注册失败:', error)
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error : new Error('注册失败')
      }))
      throw error
    }
  }

  // 登出方法
  const logout = async () => {
    setState(prev => ({ ...prev, isLoading: true }))
    try {
      await authApi.logout()
      tokenManager.clear()
      setState({
        user: null,
        isLoading: false,
        isAuthenticated: false,
        error: null
      })
    } catch (error) {
      console.error('登出失败:', error)
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error : new Error('登出失败')
      }))
    }
  }

  // 清除错误
  const clearError = () => {
    setState(prev => ({ ...prev, error: null }))
  }

  // 提供上下文值
  const contextValue: AuthContextType = {
    ...state,
    login,
    adminLogin,
    logout,
    register,
    clearError
  }

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  )
}

// 使用认证上下文的Hook
export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth必须在AuthProvider内部使用')
  }
  return context
}

// 用户角色Hook
export function useUserRole() {
  const { user } = useAuth()
  return {
    isAdmin: user?.role === 'admin',
    isUser: user?.role === 'user',
    role: user?.role || null,
  }
}
