import React from 'react'
import { QRCode } from 'qrcode.react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Loader2 } from 'lucide-react'

interface PaymentDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  codeUrl?: string
  orderNo?: string
  amount: number
  isLoading?: boolean
  onCancel?: () => void
}

export function PaymentDialog({
  open,
  onOpenChange,
  codeUrl,
  orderNo,
  amount,
  isLoading,
  onCancel,
}: PaymentDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>微信支付</DialogTitle>
          <DialogDescription>
            请使用微信扫描二维码完成支付
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="flex flex-col items-center space-y-4">
            <div className="text-lg font-semibold">
              支付金额：¥{(amount / 100).toFixed(2)}
            </div>
            {codeUrl ? (
              <div className="p-4 bg-white rounded-lg">
                <QRCode value={codeUrl} size={200} />
              </div>
            ) : isLoading ? (
              <div className="flex items-center justify-center w-[200px] h-[200px]">
                <Loader2 className="w-8 h-8 animate-spin" />
              </div>
            ) : null}
            <div className="text-sm text-gray-500">
              订单号：{orderNo}
            </div>
          </div>
        </div>
        <div className="flex justify-end">
          <Button variant="outline" onClick={onCancel}>
            取消支付
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
} 