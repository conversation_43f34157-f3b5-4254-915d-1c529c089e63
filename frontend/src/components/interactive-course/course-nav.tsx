'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Settings, ArrowLeft, Home } from 'lucide-react'

export function CourseNav() {
  return (
    <nav className="bg-white shadow-sm border-b sticky top-0 z-40">
      <div className="max-w-4xl mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/" className="flex items-center text-gray-600 hover:text-gray-900">
                <ArrowLeft className="h-4 w-4 mr-1" />
                返回首頁
              </Link>
            </Button>
            <div className="h-4 w-px bg-gray-300"></div>
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-green-600" />
              <span className="font-medium text-gray-900">n8n自動化課程</span>
            </div>
          </div>
          <Button variant="outline" size="sm" asChild>
            <Link href="/n8n" className="flex items-center">
              <Home className="h-4 w-4 mr-1" />
              了解n8n專題
            </Link>
          </Button>
        </div>
      </div>
    </nav>
  )
} 