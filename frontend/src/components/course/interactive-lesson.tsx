'use client'

import React, { useState, useEffect, useRef } from 'react'
import { ChevronRight, FileText, RefreshCw, PenLine, User } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { AIPractice } from './ai-practice'
import { ImagePractice } from './image-practice'
import { CourseStep } from '@/types/course'
import { MediaStep } from './media-step'
import { Markdown } from '@/components/markdown'

interface InteractiveLessonProps {
  title: string;
  steps: CourseStep[];
  onComplete?: () => void;
  hasNextLesson?: boolean;
}

export function InteractiveLesson({ title, steps, onComplete, hasNextLesson }: InteractiveLessonProps) {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [userInput, setUserInput] = useState('');
  const [isReviewMode, setIsReviewMode] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);
  const [showContinueButton, setShowContinueButton] = useState(true);

  // 确保 steps 不为空
  useEffect(() => {
    console.log('课程步骤:', steps);
    if (!steps || steps.length === 0) {
      console.error('没有课程步骤');
    }
  }, [steps]);

  // 获取当前步骤，添加空值检查
  const currentStep = steps?.[currentStepIndex] || {
    type: 'dialogue',
    speaker: '系统',
    content: '课程内容加载中...'
  };

  // 自动滚动到最新内容，使用 setTimeout 确保在渲染完成后滚动
  useEffect(() => {
    if (contentRef.current) {
      // 使用 setTimeout 确保在渲染完成后滚动
      setTimeout(() => {
        if (contentRef.current) {
          contentRef.current.scrollTop = contentRef.current.scrollHeight;
        }
      }, 100);
    }
  }, [currentStepIndex]);

  // 监听步骤变化
  useEffect(() => {
    if (currentStep) {
      console.log('当前步骤:', currentStep);
      if (currentStep.type === 'image_practice') {
        console.log('图片练习配置:', currentStep.image_practice);
      }
    }
  }, [currentStepIndex, currentStep]);

  const handleNext = () => {
    if (currentStepIndex < steps.length - 1) {
      setCurrentStepIndex(prev => prev + 1);
    } else if (onComplete) {
      onComplete();
    }
  };

  const handleInputSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (userInput.trim()) {
      handleNext();
    }
  };

  // 检查是否是复习模式
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    setIsReviewMode(urlParams.get('mode') === 'review');
  }, []);

  return (
    <div className="flex flex-col h-[calc(100vh-4rem)] bg-gray-50">
      {/* 对话历史区域 */}
      <div
        ref={contentRef}
        className="flex-1 overflow-y-auto px-4 py-6 pb-24"
      >
        <div className="max-w-3xl mx-auto space-y-6">
          {/* 如果是复习模式，显示所有步骤；否则只显示到当前步骤 */}
          {(isReviewMode ? steps : steps.slice(0, currentStepIndex + 1)).map((step, index) => {
            const isCurrentStep = index === currentStepIndex;

            switch (step.type) {
              case 'dialogue':
                const isSystem = step.speaker === '系统';
                const isInstructor = step.speaker === '讲师';
                const isUser = !isSystem && !isInstructor;

                return (
                  <div
                    key={index}
                    className={`flex items-start gap-3 ${isUser ? 'flex-row-reverse' : 'flex-row'}`}
                  >
                    {/* 头像 */}
                    <Avatar className="w-10 h-10 shrink-0">
                      <AvatarImage src={
                        isSystem ? "/images/system-avatar.png" :
                        isInstructor ? "/images/instructor-avatar.png" :
                        "/images/user-avatar.png"
                      } />
                      <AvatarFallback>
                        {isSystem ? 'S' : isInstructor ? 'T' : 'U'}
                      </AvatarFallback>
                    </Avatar>

                    {/* 对话内容 */}
                    <div className={`flex-1 space-y-2 ${isUser ? 'items-end' : 'items-start'}`}>
                      <div className="font-bold text-base text-gray-700">
                        {step.speaker}
                      </div>
                      <div className={`rounded-lg shadow-sm p-4 prose prose-base max-w-none ${
                        isSystem ? 'bg-blue-100 text-gray-800' :
                        isInstructor ? 'bg-green-100 text-gray-800' :
                        'bg-white text-gray-800'
                      }`}>
                        <Markdown content={step.content || ''} />
                      </div>
                    </div>
                  </div>
                );

              case 'media':
                if (step.media) {
                  // 使用更稳定的 key，基于媒体 URL 和类型
                  const mediaKey = `${step.media.type}-${step.media.url}-${index}`;
                  return (
                    <MediaStep
                      key={mediaKey}
                      media={step.media}
                      className="my-4"
                    />
                  );
                }
                return null;

              case 'wait':
                // 在复习模式下不显示等待按钮
                if (isReviewMode) return null;
                return (
                  <div key={index} className="flex justify-center">
                    <Button
                      variant="outline"
                      onClick={handleNext}
                      disabled={!isCurrentStep}
                    >
                      {step.content || '继续'}
                    </Button>
                  </div>
                );

              case 'ai_practice':
                if (step.practice) {
                  return (
                    <div key={index} className="bg-white rounded-lg shadow-sm p-6">
                      {step.content && (
                        <div className="mb-4 prose prose-sm max-w-none">
                          <Markdown content={step.content} />
                        </div>
                      )}
                      {/* 在复习模式下不显示 AI 练习组件 */}
                      {!isReviewMode && isCurrentStep && (
                        <AIPractice
                          type={step.practice.type === 'image' ? 'image' : 'chat'}
                          mode={step.practice.mode}
                          character="assistant"
                          temperature={step.practice.temperature}
                          max_tokens={step.practice.max_tokens}
                          initial_prompt={step.content || ''}
                          model={step.practice.model}
                          onComplete={handleNext}
                          placeholder="输入你的答案..."
                        />
                      )}
                    </div>
                  );
                }
                return null;

              case 'image_practice':
                if (step.image_practice) {
                  console.log('渲染图片练习步骤:', step);
                  return (
                    <div key={index} className="bg-white rounded-lg shadow-sm p-6">
                      {step.content && (
                        <div className="mb-4 whitespace-pre-wrap">{step.content}</div>
                      )}
                      {/* 在复习模式下不显示图片练习组件 */}
                      {!isReviewMode && isCurrentStep && (
                        <ImagePractice
                          mode={step.image_practice.mode}
                          size={step.image_practice.size}
                          onComplete={handleNext}
                          onStatusChange={(isCompleted) => {
                            // 根据图片练习的完成状态来控制继续按钮的显示
                            setShowContinueButton(isCompleted)
                          }}
                        />
                      )}
                    </div>
                  );
                }
                return null;

              default:
                return null;
            }
          })}
        </div>
      </div>

      {/* 底部按钮区域 - 在复习模式下不显示 */}
      {!isReviewMode && (
        <div className="fixed bottom-0 left-0 right-0 p-4 bg-white border-t">
          <div className="max-w-3xl mx-auto">
            {currentStep.type === 'image_practice' ? (
              // 图片练习步骤时，只在完成后显示继续按钮
              showContinueButton && (
                <Button
                  onClick={handleNext}
                  className="w-full bg-blue-500 hover:bg-blue-600 text-white"
                  size="lg"
                >
                  继续 <ChevronRight className="w-4 h-4 ml-1" />
                </Button>
              )
            ) : (
              // 其他步骤正常显示按钮
              currentStepIndex < steps.length - 1 ? (
                <Button
                  onClick={handleNext}
                  className="w-full bg-blue-500 hover:bg-blue-600 text-white"
                  size="lg"
                >
                  继续 <ChevronRight className="w-4 h-4 ml-1" />
                </Button>
              ) : (
                <Button
                  onClick={onComplete}
                  className="w-full bg-green-500 hover:bg-green-600 text-white"
                  size="lg"
                >
                  {hasNextLesson ? '下一节课' : '完成课程'} <ChevronRight className="w-4 h-4 ml-1" />
                </Button>
              )
            )}
          </div>
        </div>
      )}
    </div>
  );
}