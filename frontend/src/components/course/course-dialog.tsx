'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Course } from '@/lib/services/course.service';

// 1. 定義 Category 類型
interface Category {
  id: string;
  name: string;
}

// 表单验证 Schema
const courseFormSchema = z.object({
  title: z.string().min(1, '请输入课程标题'),
  description: z.string().min(1, '请输入课程描述'),
  code: z.string().min(1, '请输入课程代码').regex(/^[a-z0-9_]+$/, '课程代码只能包含小写字母、数字和下划线'),
  level: z.enum(['综合基础', '专项进阶', '技术高阶'], {
    required_error: '请选择课程难度',
  }),
  duration: z.string().min(1, '请输入课程时长'),
  price: z.string()
    .min(1, '请输入课程价格')
    .transform((val) => {
      const num = Number(val);
      if (isNaN(num)) {
        throw new Error('请输入有效的价格');
      }
      return num;
    }),
  categoryId: z.string().optional(),
});

type CourseFormValues = z.infer<typeof courseFormSchema>;

interface CourseDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: CourseFormValues) => Promise<void>;
  initialData?: Partial<Course>;
  mode?: 'create' | 'edit';
  categories?: Category[];
}

export function CourseDialog({
  open,
  onOpenChange,
  onSubmit,
  initialData,
  mode = 'create',
  categories = [],
}: CourseDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<CourseFormValues>({
    resolver: zodResolver(courseFormSchema),
    defaultValues: {
      title: initialData?.title || '',
      description: initialData?.description || '',
      code: initialData?.code || '',
      level: initialData?.level || '综合基础',
      duration: initialData?.duration || '',
      price: initialData?.price?.toString() || '0',
      categoryId: initialData?.categoryId || 'none',
    },
  });

  useEffect(() => {
    if (initialData) {
      form.reset({
        title: initialData.title || '',
        description: initialData.description || '',
        code: initialData.code || '',
        level: initialData.level || '综合基础',
        duration: initialData.duration || '',
        price: initialData.price?.toString() || '0',
        categoryId: initialData.categoryId || 'none',
      });
    }
  }, [form, initialData]);

  const handleSubmit = async (values: CourseFormValues) => {
    try {
      setIsSubmitting(true);
      const submitValues = {
        ...values,
        categoryId: values.categoryId === 'none' ? undefined : values.categoryId,
      };
      await onSubmit(submitValues);
      form.reset(); // 重置表单
      onOpenChange(false); // 关闭对话框
    } catch (error) {
      console.error('提交失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{mode === 'create' ? '创建课程' : '编辑课程'}</DialogTitle>
          <DialogDescription>
            {mode === 'create' 
              ? '创建一个新的课程。创建后默认为未发布状态。'
              : '编辑课程信息。'
            }
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>课程标题</FormLabel>
                  <FormControl>
                    <Input placeholder="输入课程标题" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="code"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>课程代码</FormLabel>
                  <FormControl>
                    <Input placeholder="输入课程代码，如：course_1" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>课程描述</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="输入课程描述"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="level"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>难度等级</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择难度等级" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="综合基础">综合基础</SelectItem>
                        <SelectItem value="专项进阶">专项进阶</SelectItem>
                        <SelectItem value="技术高阶">技术高阶</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="duration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>课程时长</FormLabel>
                    <FormControl>
                      <Input placeholder="如：2小时30分钟" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={form.control}
              name="price"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>课程价格</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      placeholder="输入课程价格"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="categoryId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>课程分类</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="请选择分类" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="none">无</SelectItem>
                      {categories.map((cat) => (
                        <SelectItem key={cat.id} value={cat.id}>{cat.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                取消
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? '提交中...' : mode === 'create' ? '创建' : '保存'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 