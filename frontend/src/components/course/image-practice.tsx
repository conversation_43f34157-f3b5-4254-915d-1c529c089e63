'use client'

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Card } from '@/components/ui/card'
import { Loader2, Send } from 'lucide-react'
import { api } from '@/lib/api'

interface ImagePracticeProps {
  mode: string
  size?: string
  onComplete?: () => void
  onStatusChange?: (isCompleted: boolean) => void
}

export function ImagePractice({ mode, size = "1024x1024", onComplete, onStatusChange }: ImagePracticeProps) {
  const [prompt, setPrompt] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [generatedImage, setGeneratedImage] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [isCompleted, setIsCompleted] = useState(false)
  const [isImageLoading, setIsImageLoading] = useState(false)

  useEffect(() => {
    if (onStatusChange) {
      onStatusChange(isCompleted)
    }
  }, [isCompleted, onStatusChange])

  const handleSubmit = async () => {
    if (!prompt.trim()) return

    setIsLoading(true)
    setError(null)
    setIsCompleted(false)
    setIsImageLoading(false)

    try {
      const response = await api.post('/api/ai/images/generate', {
        prompt,
        size,
        mode
      })

      console.log('API Response:', response.data)

      if (response.data.success && response.data.data?.length > 0) {
        const imageUrl = response.data.data[0];
        console.log('Generated Image URL:', imageUrl)
        console.log('Setting image URL to state...')
        setGeneratedImage(imageUrl)
        setIsImageLoading(true)
      } else {
        console.log('No images in response:', response.data)
        setError('生成图片失败')
      }
    } catch (err) {
      console.error('Error generating image:', err)
      setError('生成图片时发生错误，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  const handleComplete = () => {
    if (onComplete) {
      onComplete()
    }
  }

  // 添加 useEffect 来监控状态变化
  useEffect(() => {
    console.log('generatedImage state changed:', generatedImage)
  }, [generatedImage])

  console.log('Rendering with generatedImage:', generatedImage)

  return (
    <Card className="p-4">
      <div className="flex flex-col gap-4">
        {/* 生成的图片显示区域 */}
        {generatedImage && (
          <div className="flex flex-col items-center gap-4 border border-gray-200 rounded-lg p-4">
            {isImageLoading && (
              <div className="flex items-center justify-center w-full h-32">
                <div className="flex flex-col items-center gap-2">
                  <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
                  <div className="text-sm text-gray-500">图片加载中...</div>
                </div>
              </div>
            )}
            <img 
              src={generatedImage} 
              alt="AI Generated" 
              className={`max-w-full h-auto rounded-lg shadow-md transition-opacity duration-300 ${isImageLoading ? 'opacity-0' : 'opacity-100'}`}
              onLoad={() => {
                console.log('Image loaded successfully')
                setIsImageLoading(false)
                setIsCompleted(true)
              }}
              onError={(e) => {
                console.error('Image failed to load:', e)
                setIsImageLoading(false)
                setError('图片加载失败')
              }}
            />
          </div>
        )}

        {/* 错误提示 */}
        {error && (
          <div className="text-red-500 text-sm">
            {error}
          </div>
        )}

        {/* 输入区域 */}
        <div className="flex gap-2">
          <Textarea
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder="描述你想要生成的图片..."
            className="flex-1"
            disabled={isLoading || isCompleted}
          />
          <Button 
            onClick={handleSubmit} 
            disabled={isLoading || !prompt.trim() || isCompleted}
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                生成中
              </>
            ) : (
              <>
                <Send className="w-4 h-4 mr-2" />
                生成
              </>
            )}
          </Button>
        </div>
      </div>
    </Card>
  )
} 