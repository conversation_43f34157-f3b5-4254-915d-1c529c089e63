'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { CourseLesson } from '@/lib/services/course.service';

// 表单验证 Schema
const lessonFormSchema = z.object({
  title: z.string().min(1, '请输入章节标题'),
  description: z.string().min(1, '请输入章节描述'),
  duration: z.string()
    .min(1, '请输入章节时长')
    .regex(/^\d+$/, '请输入数字')
    .transform((val) => val + '分钟'), // 自动添加"分钟"单位
});

type LessonFormValues = z.infer<typeof lessonFormSchema>;

interface LessonDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: LessonFormValues) => Promise<void>;
  initialData?: Partial<CourseLesson>;
  mode?: 'create' | 'edit';
}

export function LessonDialog({
  open,
  onOpenChange,
  onSubmit,
  initialData,
  mode = 'create',
}: LessonDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<LessonFormValues>({
    resolver: zodResolver(lessonFormSchema),
    defaultValues: {
      title: initialData?.title || '',
      description: initialData?.description || '',
      duration: initialData?.duration ? initialData.duration.replace(/分钟$/, '') : '',
    },
  });

  // 在对话框打开时重新设置表单的值
  useEffect(() => {
    if (open && initialData) {
      form.reset({
        title: initialData.title || '',
        description: initialData.description || '',
        duration: initialData.duration ? initialData.duration.replace(/分钟$/, '') : '',
      });
    }
  }, [open, initialData, form]);

  const handleSubmit = async (values: LessonFormValues) => {
    try {
      setIsSubmitting(true);
      await onSubmit(values);
      form.reset(); // 重置表单
      onOpenChange(false); // 关闭对话框
    } catch (error) {
      console.error('提交失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{mode === 'create' ? '添加章节' : '编辑章节'}</DialogTitle>
          <DialogDescription>
            {mode === 'create' 
              ? '添加一个新的课程章节。'
              : '编辑课程章节信息。'
            }
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>章节标题</FormLabel>
                  <FormControl>
                    <Input placeholder="输入章节标题" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>章节描述</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="输入章节描述"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="duration"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>章节时长（分钟）</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      placeholder="如：30" 
                      {...field} 
                      onChange={(e) => field.onChange(e.target.value.replace(/\D/g, ''))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                取消
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? '提交中...' : mode === 'create' ? '添加' : '保存'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 