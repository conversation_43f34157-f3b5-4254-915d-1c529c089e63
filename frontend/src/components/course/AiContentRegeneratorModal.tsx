import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  Button,
  Space,
  message,
  Switch,
  InputNumber,
} from 'antd';
import { regenerateSection, RegenerateSectionParams } from '@/lib/api/course';
import { AxiosError } from 'axios';

const { TextArea } = Input;
const { Option } = Select;

interface AiContentRegeneratorModalProps {
  visible: boolean;
  onClose: () => void;
  onGenerated: (content: string) => void;
  selectedContent: string;
}

export const AiContentRegeneratorModal = ({
  visible,
  onClose,
  onGenerated,
  selectedContent,
}: AiContentRegeneratorModalProps) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 提取章节标题
  useEffect(() => {
    if (selectedContent) {
      const lines = selectedContent.split('\n');
      const titleLine = lines.find(line => line.trim().startsWith('## '));
      if (titleLine) {
        const title = titleLine.replace('## ', '').trim();
        form.setFieldValue('sectionTitle', title);
      }
    }
  }, [selectedContent, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const params: RegenerateSectionParams = {
        sectionTitle: values.sectionTitle,
        outline: selectedContent,
        selectedContent,
        difficulty: values.difficulty,
        interactionCount: values.interactionCount ? Number(values.interactionCount) : undefined,
        includeExercises: values.includeExercises,
      };

      const response = await regenerateSection(params);
      if (response.content) {
        onGenerated(response.content);
        message.success('内容重新生成成功！');
        onClose();
      }
    } catch (error) {
      console.error('生成失败:', error);
      const errorMessage = error instanceof AxiosError 
        ? error.response?.data?.message || error.message
        : '未知错误';
      message.error('生成失败：' + errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="AI 重新生成选中内容"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          difficulty: 'beginner',
          includeExercises: true,
          interactionCount: 20,
          sectionTitle: '',
        }}
      >
        <Form.Item
          name="sectionTitle"
          label="主题"
          rules={[{ required: true, message: '请输入要生成的主题' }]}
        >
          <Input placeholder="请输入要生成的主题，如果选中的内容包含章节标题会自动填充" />
        </Form.Item>

        <Form.Item
          label="选中的内容"
        >
          <TextArea
            rows={6}
            value={selectedContent}
            disabled
          />
        </Form.Item>

        <Space size="large" style={{ width: '100%', justifyContent: 'space-between' }}>
          <Form.Item
            name="difficulty"
            label="难度级别"
            style={{ width: '200px' }}
          >
            <Select>
              <Option value="beginner">综合基础</Option>
              <Option value="intermediate">专项进阶</Option>
              <Option value="advanced">技术高阶</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="interactionCount"
            label="交互轮数"
            style={{ width: '200px' }}
          >
            <InputNumber min={10} max={50} style={{ width: '100%' }} />
          </Form.Item>
        </Space>

        <Form.Item
          name="includeExercises"
          label="包含练习"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>

        <Form.Item>
          <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" loading={loading} onClick={handleSubmit}>
              开始生成
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
}; 