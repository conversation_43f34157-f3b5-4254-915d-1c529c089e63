import React, { useState } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  Button,
  Space,
  message,
  Switch,
  InputNumber,
} from 'antd';
import { generateCourse, regenerateSection, CourseGenerationParams, RegenerateSectionParams } from '@/lib/api/course';
import { AxiosError } from 'axios';

const { TextArea } = Input;
const { Option } = Select;

interface AiCourseGeneratorProps {
  visible: boolean;
  onClose: () => void;
  onGenerated: (content: string) => void;
  currentContent?: string;
  sectionTitle?: string;
  selectedContent?: string;
}

export const AiCourseGenerator = ({
  visible,
  onClose,
  onGenerated,
  currentContent,
  sectionTitle,
  selectedContent,
}: AiCourseGeneratorProps) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 根据模式构建不同的参数
      if (sectionTitle) {
        // 重新生成小节模式
        const params: RegenerateSectionParams = {
          sectionTitle,
          outline: currentContent || '',
          difficulty: values.difficulty,
          interactionCount: values.interactionCount ? Number(values.interactionCount) : undefined,
          includeExercises: values.includeExercises,
          selectedContent,
        };
        const response = await regenerateSection(params);
        if (response.content) {
          onGenerated(response.content);
          message.success('内容生成成功！');
          onClose();
        }
      } else {
        // 生成新课程模式
        const params: CourseGenerationParams = {
          title: values.title,
          rawMaterial: selectedContent || values.rawMaterial,
          difficulty: values.difficulty,
          targetAudience: values.targetAudience,
          tone: values.tone,
          includeExercises: values.includeExercises,
          sectionCount: values.sectionCount ? Number(values.sectionCount) : undefined,
          interactionCount: values.interactionCount ? Number(values.interactionCount) : undefined,
        };
        const response = await generateCourse(params);
        if (response.content) {
          onGenerated(response.content);
          message.success('内容生成成功！');
          onClose();
        }
      }
    } catch (error) {
      console.error('生成失败:', error);
      const errorMessage = error instanceof AxiosError 
        ? error.response?.data?.message || error.message
        : '未知错误';
      message.error('生成失败：' + errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={sectionTitle ? (selectedContent ? 'AI 重新生成选中内容' : 'AI 重新生成小节') : 'AI 生成课程'}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          difficulty: 'beginner',
          tone: 'casual',
          includeExercises: true,
          sectionCount: 3,
          interactionCount: 20,
          rawMaterial: selectedContent || '',
        }}
      >
        <Form.Item
          name="title"
          label="课程标题"
          rules={[{ required: true, message: '请输入课程标题' }]}
        >
          <Input placeholder="请输入课程标题" />
        </Form.Item>

        <Form.Item
          name="rawMaterial"
          label={selectedContent ? "选中的内容" : "原始素材"}
          rules={[{ required: true, message: '请输入原始素材' }]}
        >
          <TextArea
            rows={6}
            placeholder={selectedContent ? "这是您选中的内容，将基于此重新生成" : "请输入课程原始素材，可以是大纲、关键点或完整文档"}
            disabled={!!selectedContent}
          />
        </Form.Item>

        <Form.Item
          name="targetAudience"
          label="目标受众"
          rules={[{ required: true, message: '请输入目标受众' }]}
        >
          <Input placeholder="例如：初学者、有经验的开发者等" />
        </Form.Item>

        <Space size="large" style={{ width: '100%', justifyContent: 'space-between' }}>
          <Form.Item
            name="difficulty"
            label="难度级别"
            style={{ width: '200px' }}
          >
            <Select>
              <Option value="beginner">综合基础</Option>
              <Option value="intermediate">专项进阶</Option>
              <Option value="advanced">技术高阶</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="tone"
            label="语气风格"
            style={{ width: '200px' }}
          >
            <Select>
              <Option value="casual">轻松友好</Option>
              <Option value="formal">正式专业</Option>
            </Select>
          </Form.Item>
        </Space>

        {!sectionTitle && (
          <Space size="large" style={{ width: '100%', justifyContent: 'space-between' }}>
            <Form.Item
              name="sectionCount"
              label="小节数量"
              style={{ width: '200px' }}
            >
              <InputNumber min={1} max={10} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="interactionCount"
              label="交互轮数"
              style={{ width: '200px' }}
            >
              <InputNumber min={10} max={50} style={{ width: '100%' }} />
            </Form.Item>
          </Space>
        )}

        <Form.Item
          name="includeExercises"
          label="包含练习"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>

        <Form.Item>
          <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" loading={loading} onClick={handleSubmit}>
              开始生成
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
}; 