'use client';

import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Clock, BookOpen } from 'lucide-react';
import Link from 'next/link';

interface CourseCardProps {
  id: string;
  title: string;
  description: string;
  duration: string;
  level: '综合基础' | '专项进阶' | '技术高阶';
  lessonsCount: number;
  price: number;
  isPurchased?: boolean;
}

export function CourseCard({
  id,
  title,
  description,
  duration,
  level,
  lessonsCount,
  price,
  isPurchased = false,
}: CourseCardProps) {
  // 根据难度级别设置不同的背景色
  const getBgColorClass = (level: '综合基础' | '专项进阶' | '技术高阶') => {
    switch (level) {
      case '综合基础':
        return 'bg-blue-50/50 hover:bg-blue-50';
      case '专项进阶':
        return 'bg-purple-50/50 hover:bg-purple-50';
      case '技术高阶':
        return 'bg-orange-50/50 hover:bg-orange-50';
      default:
        return '';
    }
  };

  return (
    <Card className={`flex flex-col h-full transition-all duration-300 border-slate-100 ${getBgColorClass(level)}`}>
      <CardHeader>
        <div className="flex items-center justify-between mb-2">
          <Badge variant="secondary" className={`${
            level === '综合基础' ? 'bg-blue-100 text-blue-700' :
            level === '专项进阶' ? 'bg-purple-100 text-purple-700' :
            'bg-orange-100 text-orange-700'
          } hover:bg-opacity-80`}>{level}</Badge>
          <Badge variant="default" className={`${
            Number(price) === 0 
              ? "bg-green-50 text-green-600 hover:bg-green-100" 
              : "bg-indigo-50 text-indigo-600 hover:bg-indigo-100"
          }`}>
            {Number(price) === 0 ? "免费" : `$${price}`}
          </Badge>
        </div>
        <h3 className="text-lg font-semibold text-slate-800">{title}</h3>
      </CardHeader>
      <CardContent className="flex-grow">
        <p className="text-sm text-slate-600 line-clamp-2">{description}</p>
        <div className="flex items-center gap-4 mt-4 text-sm text-slate-500">
          <div className="flex items-center gap-1">
            <Clock className="h-4 w-4 text-slate-400" />
            <span>{duration}</span>
          </div>
          <div className="flex items-center gap-1">
            <BookOpen className="h-4 w-4 text-slate-400" />
            <span>{lessonsCount} 课时</span>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button 
          asChild 
          className={`w-full ${
            isPurchased 
              ? 'bg-emerald-600 hover:bg-emerald-700' 
              : Number(price) === 0 
                ? 'bg-green-600 hover:bg-green-700'
                : 'bg-indigo-600 hover:bg-indigo-700'
          } text-white`}
        >
          <Link href={
            isPurchased 
              ? `/my-courses/${id}` 
              : Number(price) === 0
                ? `/all-courses/free-enroll/${id}`
                : `/all-courses/${id}`
          }>
            {isPurchased 
              ? "进入学习" 
              : Number(price) === 0
                ? "立即加入"
                : "了解详情"
            }
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
} 