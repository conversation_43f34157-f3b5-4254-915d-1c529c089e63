"use client"

import { motion } from 'framer-motion'
import { Zap, Settings, Database } from 'lucide-react'

export default function N8nFeaturesSection() {
  const features = [
    {
      name: '開源免費',
      description: 'n8n 完全開源、可自架，無用量限制，彈性高。',
      icon: Zap,
    },
    {
      name: '無需程式基礎',
      description: '介面直覺，拖拉節點即可設計自動化流程，人人都能上手。',
      icon: Settings,
    },
    {
      name: '豐富整合',
      description: '支援 200+ 服務串接，API、資料庫、雲端工具一應俱全。',
      icon: Database,
    },
  ]
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-green-700">n8n 特色亮點</h2>
          <p className="mt-4 text-xl text-gray-700">為什麼選擇 n8n？</p>
        </div>
        <div className="mt-16 grid grid-cols-1 gap-12 lg:grid-cols-3">
          {features.map((feature, index) => (
            <motion.div
              key={feature.name}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="relative"
            >
              <div className="absolute flex items-center justify-center h-12 w-12 rounded-md bg-green-500 text-white">
                <feature.icon className="h-6 w-6" />
              </div>
              <div className="ml-16">
                <h3 className="text-xl font-medium text-green-700">{feature.name}</h3>
                <p className="mt-2 text-base text-gray-700">{feature.description}</p>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
} 