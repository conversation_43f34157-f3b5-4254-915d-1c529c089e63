"use client"

import { motion } from 'framer-motion'
import { Zap, Settings, Database } from 'lucide-react'

export default function N8nScenarioSection() {
  const scenarios = [
    {
      title: '自動化日常工作',
      icon: Zap,
      description: '自動同步 Google Sheet、Notion、Airtable 等資料，節省大量重複操作時間。',
    },
    {
      title: '跨平台整合',
      icon: Settings,
      description: '串接 Email、Slack、Line、API 等多種服務，打造專屬自動化流程。',
    },
    {
      title: '數據自動備份',
      icon: Database,
      description: '自動備份、整理資料，確保資訊安全與高效管理。',
    },
  ]
  return (
    <section className="py-20 bg-gradient-to-br from-green-50 via-white to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-green-700">n8n 應用場景</h2>
          <p className="mt-4 text-xl text-gray-700">發現 n8n 在各種自動化場景的強大應用</p>
        </div>
        <div className="mt-16 grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {scenarios.map((scenario, index) => (
            <motion.div
              key={scenario.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="relative bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-shadow duration-300"
            >
              <div className="inline-flex items-center justify-center p-3 bg-gradient-to-br from-green-100 to-blue-100 rounded-xl">
                <scenario.icon className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="mt-6 text-2xl font-semibold text-green-700">{scenario.title}</h3>
              <p className="mt-2 text-gray-700">{scenario.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
} 