"use client"

import { motion } from 'framer-motion'
import Link from 'next/link'
import { ArrowRight } from 'lucide-react'

export default function N8nHeroSection() {
  return (
    <div className="relative overflow-hidden bg-gradient-to-br from-green-50 via-white to-blue-50">
      <div
        aria-hidden="true"
        className="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]"
      />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="relative z-10 py-16 sm:py-24 md:py-32">
          <div className="text-center max-w-3xl mx-auto">
            <motion.h1 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-4xl tracking-tight font-extrabold text-green-700 sm:text-5xl md:text-6xl"
            >
              <span className="block">n8n 自動化專題</span>
              <span className="block bg-clip-text text-transparent bg-gradient-to-r from-green-600 to-blue-600">開啟你的自動化之路</span>
            </motion.h1>
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mt-6 text-xl text-gray-700 max-w-2xl mx-auto"
            >
              n8n 是開源自動化工具，讓你無需寫程式也能串接各種服務，打造專屬自動化流程。立即了解 n8n 如何提升你的工作效率，並探索我們精心設計的 n8n 實戰課程！
            </motion.p>
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="mt-8 flex flex-col sm:flex-row gap-4 justify-center items-center"
            >
              <Link href="/interactive-course" className="px-8 py-3 flex items-center justify-center bg-green-600 hover:bg-green-700 text-white rounded-md text-lg font-medium transition-colors">
                免费立即參加 n8n實戰課程
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  )
} 