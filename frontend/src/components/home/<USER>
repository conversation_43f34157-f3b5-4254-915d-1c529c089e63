'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useAuth } from '@/auth/contexts/simplified-auth-context'
import { BookOpen } from 'lucide-react'
import { cn } from '@/lib/utils'

export function LandingNav() {
  const pathname = usePathname()
  const { isAuthenticated, isLoading, logout } = useAuth()
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  const handleLogout = async () => {
    try {
      await logout()
      console.log('[LandingNav] 登出成功', {
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error('[LandingNav] 登出失败:', error)
    }
  }

  // 基础样式
  const linkBaseStyles = "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 h-10 px-4 py-2"
  const linkActiveStyles = "bg-primary text-white hover:bg-primary/90"
  const linkInactiveStyles = "text-gray-600 hover:text-primary hover:bg-secondary"

  // 统一的导航结构
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-white/80 backdrop-blur-sm shadow-sm">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex-shrink-0 flex items-center">
          <Link href="/" className="flex items-center">
            <BookOpen className="mr-2 h-6 w-6 text-primary" />
            <span className="text-2xl font-bold text-primary">
              How to AI with｜AI應用課
            </span>
          </Link>
        </div>
        

        {/* 导航链接 */}
        <div className={cn(
          "hidden sm:flex sm:items-center sm:space-x-4",
          (isClient && !isLoading) ? "opacity-100" : "opacity-0 hidden"
        )}>
          <Link
            href="/all-courses"
            className={cn(
              linkBaseStyles,
              pathname?.startsWith('/all-courses') ? linkActiveStyles : linkInactiveStyles
            )}
          >
            所有課程
          </Link>
          
          {/* 认证状态导航 */}
          <div className={cn(
            "flex items-center space-x-4",
            isAuthenticated ? "opacity-100" : "opacity-0 hidden"
          )}>
            <Link
              href="/dashboard"
              className={cn(
                linkBaseStyles,
                pathname?.startsWith('/dashboard') ? linkActiveStyles : linkInactiveStyles
              )}
            >
              個人面板
            </Link>
            <button
              onClick={handleLogout}
              className={cn(linkBaseStyles, linkInactiveStyles)}
            >
              登出
            </button>
          </div>

          {/* 未认证状态导航 */}
          <div className={cn(
            "flex items-center space-x-4",
            !isAuthenticated ? "opacity-100" : "opacity-0 hidden"
          )}>
            <Link
              href="/login"
              className={cn(
                linkBaseStyles,
                pathname === '/login' ? linkActiveStyles : linkInactiveStyles
              )}
            >
              登入
            </Link>
            <Link
              href="/register"
              className={cn(
                linkBaseStyles,
                pathname === '/register' ? linkActiveStyles : linkInactiveStyles
              )}
            >
              免費註冊
            </Link>
          </div>
        </div>
      </div>
    </header>
  )
} 