'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Star, Users, Clock, ArrowRight } from 'lucide-react'
import { CourseService } from '@/lib/services/course.service'

export function PopularCourses() {
  const [isClient, setIsClient] = useState(false)
  const [courses, setCourses] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    setIsClient(true)
    
    const fetchCourses = async () => {
      try {
        const allCourses = await CourseService.getCourses()
        setCourses(allCourses.slice(0, 3))
      } catch (error) {
        console.error('Error fetching courses:', error)
        setCourses([])
      } finally {
        setIsLoading(false)
      }
    }

    fetchCourses()
  }, [])

  // 统一的骨架屏UI
  const SkeletonUI = () => (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900">熱門AI課程</h2>
          <p className="mt-4 text-xl text-gray-600">精選實用AI課程，助你快速掌握AI應用</p>
        </div>
        <div className="mt-16 grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3].map((index) => (
            <div key={index} className="bg-white rounded-2xl shadow-xl overflow-hidden animate-pulse">
              <div className="p-6">
                <div className="h-6 bg-gray-200 rounded w-3/4 mb-4" />
                <div className="h-4 bg-gray-200 rounded w-full mb-4" />
                <div className="h-4 bg-gray-200 rounded w-2/3" />
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )

  // 课程卡片组件
  const CourseCard = ({ course, index }) => (
    <motion.div
      key={course.id}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="bg-white rounded-2xl shadow-md hover:shadow-lg transition-shadow duration-300 p-6 border border-slate-100"
    >
      <div className="flex justify-between items-start mb-4">
        <h3 className="text-xl font-semibold text-slate-800">{course.title}</h3>
        <div className="px-2 py-1 bg-indigo-50 text-indigo-600 rounded-full text-sm font-medium">
          {course.price === 0 ? '免費' : "邀測中"}
        </div>
      </div>
      <p className="text-slate-600 mb-6">{course.description}</p>
      <div className="flex items-center justify-between text-sm text-slate-500 mb-6">
        <div className="flex items-center">
          <Star className="h-5 w-5 text-amber-400" />
          <span className="ml-1">{course.rating || '暫無評分'}</span>
        </div>
        <div className="flex items-center">
          <Users className="h-5 w-5 text-slate-400" />
          <span className="ml-1">{course.studentCount || 0}人學習</span>
        </div>
        <div className="flex items-center">
          <Clock className="h-5 w-5 text-slate-400" />
          <span className="ml-1">{course.duration || '未知'}</span>
        </div>
      </div>
      <div className="w-full">
        <Link href={`/all-courses/${course.id}`} className="w-full">
          <button className="w-full bg-indigo-600 hover:bg-indigo-700 text-white rounded-md py-2 px-4 text-sm font-medium transition-colors">
            立即學習
          </button>
        </Link>
      </div>
    </motion.div>
  )

  // 主要内容
  const MainContent = () => (
    <section className="py-20 bg-slate-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-slate-800">熱門AI課程</h2>
          <p className="mt-4 text-xl text-slate-600">精選實用AI課程，助你快速掌握AI應用</p>
        </div>

        <div className="mt-16 grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {courses.map((course, index) => (
            <CourseCard key={course.id} course={course} index={index} />
          ))}
        </div>

        <div className="mt-12 text-center">
          <Link 
            href="/all-courses"
            className="inline-flex items-center justify-center px-8 py-3 border border-indigo-200 text-indigo-600 hover:bg-indigo-50 rounded-md text-sm font-medium transition-colors"
          >
            查看全部課程
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </div>
      </div>
    </section>
  )

  // 根据状态返回相应的UI
  if (!isClient || isLoading) {
    return <SkeletonUI />
  }

  return <MainContent />
} 