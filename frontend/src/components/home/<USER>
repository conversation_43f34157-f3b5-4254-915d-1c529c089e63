'use client'

import { motion } from 'framer-motion'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"

const faqs = [
  {
    question: "我完全沒有技術背景，可以學習嗎？",
    answer: "當然可以！我們的課程專為零基礎學員設計，內容淺顯易懂，配合實戰案例，讓你輕鬆入門AI應用。"
  },
  {
    question: "學習需要多長時間？",
    answer: "依照你選擇的課程與學習步調而定。每門課程都標示建議時長，通常3-6小時即可完成。你可彈性安排時間學習。"
  },
  {
    question: "課程會持續更新嗎？",
    answer: "會的！購買後可持續獲得課程更新，確保內容緊貼AI最新應用趨勢。"
  },
  {
    question: "有實作或練習機會嗎？",
    answer: "每門課程都包含大量實作練習，你可直接在平台操作，並獲得AI助教即時回饋。"
  },
  {
    question: "有退款保障嗎？",
    answer: "我們提供7天無條件退款保證。若不滿意課程，7天內可申請全額退費。"
  }
]

export function FAQ() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900">常見問題</h2>
          <p className="mt-4 text-xl text-gray-600">解答你的疑問</p>
        </div>

        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mt-12"
        >
          <Accordion type="single" collapsible className="w-full">
            {faqs.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index}`}>
                <AccordionTrigger className="text-left">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent>
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </motion.div>
      </div>
    </section>
  )
} 