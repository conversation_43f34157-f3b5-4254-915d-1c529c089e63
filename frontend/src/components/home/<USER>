'use client'

import Image from 'next/image'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Check, Bot, Rocket, Target } from 'lucide-react'

const features = [
  {
    name: '零基礎入門',
    description: '無需任何技術背景，圖文與影片教學，手把手帶你學會AI工具。',
    icon: Target,
    benefits: [
      '圖文影片教學',
      '實戰案例解析',
      '循序漸進學習'
    ]
  },
  {
    name: 'AI助教陪學',
    description: '專屬AI助教全程陪伴，解答疑問，協助練習，讓學習更輕鬆。',
    icon: Bot,
    benefits: [
      '智能問答解惑',
      '即時反饋指導',
      '個人化建議'
    ]
  },
  {
    name: '學完即用',
    description: '課程設計貼近生活與工作場景，學完馬上能用，立即提升效率。',
    icon: Rocket,
    benefits: [
      '場景式教學',
      '實戰專案練習',
      '效果立見'
    ]
  }
]

export function FeaturesSection() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900">平台特色</h2>
          <p className="mt-4 text-xl text-gray-600">讓每個人都能輕鬆學會AI應用</p>
        </div>

        <div className="mt-16">
          <div className="grid grid-cols-1 gap-12 lg:grid-cols-3">
            {features.map((feature, index) => (
              <motion.div
                key={feature.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="relative"
              >
                <div>
                  <div className="absolute flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white">
                    {feature.icon && <feature.icon className="h-6 w-6" />}
                  </div>
                  <div className="ml-16">
                    <h3 className="text-xl font-medium text-gray-900">{feature.name}</h3>
                    <p className="mt-2 text-base text-gray-600">{feature.description}</p>
                    <ul className="mt-4 space-y-3">
                      {feature.benefits.map((benefit) => (
                        <li key={benefit} className="flex items-center text-gray-600">
                          <svg className="h-5 w-5 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
} 