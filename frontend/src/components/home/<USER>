'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { Briefcase, Palette, Lightbulb } from 'lucide-react'

const scenarios = [
  {
    title: '職場提效',
    icon: Briefcase,
    description: '掌握AI辦公技能，提升工作效率',
    features: [
      '智能文案產生',
      'PPT一鍵生成',
      '數據分析助理'
    ],
    image: '/images/scenarios/work.png'
  },
  {
    title: '生活應用',
    icon: Palette,
    description: '讓AI融入生活，提升生活品質',
    features: [
      '圖片智能處理',
      '影片剪輯助手',
      '英語學習夥伴'
    ],
    image: '/images/scenarios/life.png'
  },
  {
    title: '興趣探索',
    icon: Lightbulb,
    description: '發掘AI創意，激發無限可能',
    features: [
      'AI繪圖創作',
      '音樂智能製作',
      '創意寫作助手'
    ],
    image: '/images/scenarios/hobby.png'
  }
]

export function ScenarioSection() {
  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900">AI應用場景</h2>
          <p className="mt-4 text-xl text-gray-600">發現AI在不同場景的強大應用</p>
        </div>

        <div className="mt-16 grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {scenarios.map((scenario, index) => (
            <motion.div
              key={scenario.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="relative bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-shadow duration-300"
            >
              <div className="inline-flex items-center justify-center p-3 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-xl">
                {scenario.icon && <scenario.icon className="h-6 w-6 text-blue-600" />}
              </div>
              <h3 className="mt-6 text-2xl font-semibold text-gray-900">{scenario.title}</h3>
              <p className="mt-2 text-gray-600">{scenario.description}</p>
              <ul className="mt-6 space-y-4">
                {scenario.features.map((feature) => (
                  <li key={feature} className="flex items-center text-gray-600">
                    <svg className="h-5 w-5 text-blue-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-lg">{feature}</span>
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
} 