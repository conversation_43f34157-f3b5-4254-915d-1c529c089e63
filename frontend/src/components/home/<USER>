import { LandingNav } from './landing-nav'
import { HeroSection } from './hero-section'
import { ScenarioSection } from './scenario-section'
import { FeaturesSection } from './features-section'
import { FAQ } from './faq'
import { Footer } from './footer'
import { AuthContentWrapper } from './auth-content-wrapper'
import { N8nTopicSection } from './n8n-topic-section'

export function HomePageWrapper() {
  return (
    <div className="flex min-h-screen flex-col bg-white">
      <LandingNav />
      <main>
        <HeroSection />
        <N8nTopicSection />
        <ScenarioSection />
        <FeaturesSection />
        <AuthContentWrapper />
        <FAQ />
      </main>
      <Footer />
    </div>
  )
} 