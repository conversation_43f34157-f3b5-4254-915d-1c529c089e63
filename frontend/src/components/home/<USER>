import Link from 'next/link'
import { ArrowRight } from 'lucide-react'

export function N8nTopicSection() {
  return (
    <section className="py-16 bg-gradient-to-r from-green-50 to-blue-50">
      <div className="max-w-5xl mx-auto px-4 text-center">
        <h2 className="text-3xl md:text-4xl font-bold text-green-700 mb-4">n8n 自動化專題：解鎖高效工作流</h2>
        <p className="text-lg md:text-xl text-gray-700 mb-6">
          n8n 是開源自動化工具，幫助你無需程式基礎也能串接各種服務，打造屬於自己的自動化流程。立即了解 n8n 如何提升你的工作效率，並探索我們精心設計的 n8n 實戰課程！
        </p>
        <Link href="/n8n" className="inline-flex items-center px-8 py-3 bg-green-600 hover:bg-green-700 text-white rounded-md text-lg font-medium transition-colors">
          了解 n8n 專題與課程
          <ArrowRight className="ml-2 h-5 w-5" />
        </Link>
      </div>
    </section>
  )
} 