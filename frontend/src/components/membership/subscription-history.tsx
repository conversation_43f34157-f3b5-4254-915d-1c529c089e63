'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { format } from 'date-fns'
import { Clock, CreditCard } from 'lucide-react'

interface Subscription {
  id: string
  planName: string
  price: number
  startDate: string
  endDate: string
  isActive: boolean
}

interface SubscriptionHistoryProps {
  subscriptions: Subscription[]
}

export function SubscriptionHistory({ subscriptions }: SubscriptionHistoryProps) {
  if (!subscriptions?.length) {
    return (
      <Card>
        <CardContent className="p-6 text-center text-gray-500">
          暂无订阅记录
        </CardContent>
      </Card>
    )
  }

  const getSubscriptionStatus = (subscription: Subscription) => {
    const now = new Date();
    const startDate = new Date(subscription.startDate);
    const endDate = new Date(subscription.endDate);

    if (!subscription.isActive) {
      return { label: '已失效', variant: 'secondary' as const };
    }
    
    if (startDate > now) {
      return { label: '未生效', variant: 'outline' as const };
    }
    
    if (endDate <= now) {
      return { label: '已过期', variant: 'secondary' as const };
    }
    
    return { label: '生效中', variant: 'default' as const };
  };

  return (
    <div className="space-y-4">
      {subscriptions.map(sub => {
        const status = getSubscriptionStatus(sub);
        return (
          <Card key={sub.id}>
            <CardContent className="flex justify-between items-center p-6">
              <div className="flex items-center gap-4">
                <div className="p-2 bg-primary/10 rounded-full">
                  <CreditCard className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <div className="font-medium">{sub.planName}</div>
                  <div className="text-sm text-gray-500 flex items-center gap-1 mt-1">
                    <Clock className="w-4 h-4" />
                    {format(new Date(sub.startDate), 'yyyy-MM-dd')} 至{' '}
                    {format(new Date(sub.endDate), 'yyyy-MM-dd')}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-medium">¥{sub.price}</div>
                <Badge 
                  variant={status.variant}
                  className="mt-1"
                >
                  {status.label}
                </Badge>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  )
} 