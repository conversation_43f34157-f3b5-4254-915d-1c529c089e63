'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Crown, Calendar, RefreshCw } from 'lucide-react'
import { MembershipLevel } from '@/lib/services/user.service'
import { format } from 'date-fns'

interface MembershipStatusCardProps {
  membershipLevel: MembershipLevel
  expiryDate?: string
  autoRenew?: boolean
}

export function MembershipStatusCard({ membershipLevel, expiryDate, autoRenew }: MembershipStatusCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-2xl font-bold flex items-center gap-2">
          <Crown className="w-6 h-6 text-primary" />
          会员状态
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-gray-600">当前等级</span>
          <Badge variant={membershipLevel === MembershipLevel.PREMIUM ? 'default' : 'secondary'}>
            {membershipLevel === MembershipLevel.PREMIUM ? '高级会员' : '普通用户'}
          </Badge>
        </div>
        
        {expiryDate && (
          <div className="flex items-center justify-between">
            <span className="text-gray-600">到期时间</span>
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-gray-500" />
              <span>{format(new Date(expiryDate), 'yyyy-MM-dd')}</span>
            </div>
          </div>
        )}

        {membershipLevel === MembershipLevel.PREMIUM && (
          <div className="flex items-center justify-between">
            <span className="text-gray-600">自动续费</span>
            <div className="flex items-center gap-2">
              <RefreshCw className={`w-4 h-4 ${autoRenew ? 'text-green-500' : 'text-gray-500'}`} />
              <span>{autoRenew ? '已开启' : '已关闭'}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
} 