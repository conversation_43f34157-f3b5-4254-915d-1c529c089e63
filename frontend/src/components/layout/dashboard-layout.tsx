import { UserNav } from './user-nav'
import { Nav } from './nav'
import { MobileNav } from './mobile-nav'
import { Logo } from './logo'
import { LayoutDashboard, BookOpen, User, GraduationCap } from 'lucide-react'

const menuItems = [
  {
    title: '所有課程',
    href: '/all-courses',
    icon: GraduationCap,
  },
  {
    title: '個人面板',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    title: '我的課程',
    href: '/my-courses',
    icon: BookOpen,
  },
  {
    title: '個人資料',
    href: '/profile',
    icon: User,
  },
]

// 將需要客户端功能的导航部分提取为客户端组件
function ClientNav() {
  'use client'
  
  return (
    <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
      <div className="w-full flex-1 md:w-auto md:flex-none">
        <Nav items={menuItems} />
      </div>
      <UserNav />
    </div>
  )
}

// 將移动端导航提取为客户端组件
function ClientMobileNav() {
  'use client'
  
  return <MobileNav items={menuItems} />
}

// 主布局保持为服务器组件
export function DashboardLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="flex min-h-screen flex-col bg-secondary">
      <header className="sticky top-0 z-50 w-full border-b bg-white/80 backdrop-blur-sm shadow-sm">
        <div className="container flex h-16 items-center">
          <div className="mr-4 hidden md:flex">
            <Logo className="text-primary" />
          </div>
          <ClientMobileNav />
          <ClientNav />
        </div>
      </header>
      <main className="flex-1 container py-8">{children}</main>
    </div>
  )
} 