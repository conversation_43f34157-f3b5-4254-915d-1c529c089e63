'use client'

import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Loader2 } from 'lucide-react'
import { toast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'

// 登录表单验证模式
const loginSchema = z.object({
  email: z.string()
    .email('請輸入有效的信箱地址'),
  password: z.string()
    .min(1, '請輸入密碼'),
})

type FormData = z.infer<typeof loginSchema>

interface LoginFormProps {
  onSubmit: (data: FormData) => Promise<void>
}

export function LoginForm({ onSubmit }: LoginFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  const form = useForm<FormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  })

  const handleSubmit = async (data: FormData) => {
    try {
      console.log('[LoginForm] 表單提交開始', {
        data,
        timestamp: new Date().toISOString()
      })
      setIsLoading(true)
      await onSubmit(data)
      console.log('[LoginForm] 表單提交成功', {
        timestamp: new Date().toISOString()
      })
    } catch (error: any) {
      // 根據錯誤類型顯示不同的錯誤信息
      let errorMessage = '登入失敗，請稍後重試'

      console.error('[LoginForm] 表單提交失敗', {
        error,
        errorCode: error?.code,
        errorMessage: error?.message,
        timestamp: new Date().toISOString()
      })

      if (error?.code === 'INVALID_CREDENTIALS') {
        errorMessage = error.message || '信箱或密碼錯誤'
      } else if (error?.code === 'NETWORK_ERROR') {
        errorMessage = '網路連接失敗，請檢查網路後重試'
      }

      toast({
        title: '登入失敗',
        description: errorMessage,
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className={cn(
      "transition-opacity duration-200",
      isClient ? "opacity-100" : "opacity-0"
    )}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>信箱</FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder="請輸入信箱地址"
                    disabled={isLoading}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>密碼</FormLabel>
                <FormControl>
                  <Input
                    type="password"
                    placeholder="請輸入密碼"
                    disabled={isLoading}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button
            type="submit"
            disabled={isLoading}
            className="w-full"
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            登入
          </Button>
        </form>
      </Form>
    </div>
  )
}