'use client';

import { useEffect, useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Course, CourseService } from '@/lib/services/course.service';
import { Clock, BookOpen, AlertCircle } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { Input } from '@/components/ui/input';
import { api } from '@/lib/api';
import Link from 'next/link';
import { useAuth } from '@/auth/contexts/simplified-auth-context';
import { StripePaymentDialog } from '@/components/payment/stripe-payment-dialog';
import { useOrderPolling } from '@/hooks/useOrderPolling';

// 检查邀测码是否是用户自己生成的
const isUserOwnInviteCode = (userId: string, inviteCode: string): boolean => {
  try {
    // 从localStorage获取邀测码到用户的映射
    const codeToUserStr = localStorage.getItem('inviteCodeToUser');
    if (!codeToUserStr) return false;

    const codeToUser = JSON.parse(codeToUserStr);

    // 检查这个邀测码是否是当前用户生成的
    return codeToUser[inviteCode] === userId;
  } catch (error) {
    console.error('检查邀测码失败:', error);
    return false;
  }
};

export default function CoursePurchasePage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const { isAuthenticated, user } = useAuth();
  const courseId = params.id as string;

  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [giftCode, setGiftCode] = useState('');
  const [redeeming, setRedeeming] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [isOwnCode, setIsOwnCode] = useState(false);

  // 支付相关状态
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false);
  const [orderNo, setOrderNo] = useState<string | null>(null);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [creatingOrder, setCreatingOrder] = useState(false);

  useEffect(() => {
    setIsClient(true);

    const fetchCourse = async () => {
      try {
        const data = await CourseService.getCourse(courseId);
        if (data.isPurchased) {
          router.replace(`/all-courses/${courseId}`);
          return;
        }
        setCourse(data);
      } catch (err) {
        setError('加载课程信息失败，请稍后重试');
        console.error('Failed to fetch course:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchCourse();
  }, [courseId, router]);

  // 当邀测码输入变化时，检查是否是自己的邀测码
  const handleGiftCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const code = e.target.value;
    setGiftCode(code);

    // 如果用户已登录且输入了邀测码，检查是否是自己的邀测码
    if (isAuthenticated && user?.id && code.trim()) {
      setIsOwnCode(isUserOwnInviteCode(user.id, code.trim()));
    } else {
      setIsOwnCode(false);
    }
  };

  // 订单状态轮询
  const { startPolling, stopPolling } = useOrderPolling(
    // 支付成功回调
    () => {
      setPaymentDialogOpen(false);
      toast({
        title: '支付成功',
        description: `您已成功购买课程《${course?.title}》`,
      });
      setTimeout(() => {
        router.push(`/my-courses/${courseId}`);
      }, 1500);
    },
    // 订单过期回调
    () => {
      setPaymentDialogOpen(false);
      toast({
        variant: 'destructive',
        title: '订单已过期',
        description: '请重新创建订单',
      });
    }
  );

  // 创建订单并支付
  const handlePurchase = async () => {
    if (!course) return;

    if (!isAuthenticated) {
      toast({
        variant: 'destructive',
        title: '请先登录',
        description: '购买课程需要先登录您的账号'
      });

      // 保存当前页面URL，登录后可以返回
      const returnUrl = encodeURIComponent(window.location.pathname);
      router.push(`/login?returnUrl=${returnUrl}`);
      return;
    }

    setCreatingOrder(true);
    try {
      // 创建订单
      const response = await api.post('/api/payment/orders', { courseId });
      const { orderNo: newOrderNo } = response.data;

      // 获取Stripe支付意向
      const paymentResponse = await api.get(`/api/payment/create-payment-intent/${newOrderNo}`);
      const { clientSecret: newClientSecret } = paymentResponse.data;

      if (!newClientSecret) {
        throw new Error('获取支付信息失败');
      }

      setOrderNo(newOrderNo);
      setClientSecret(newClientSecret);
      setPaymentDialogOpen(true);

      // 开始轮询订单状态
      startPolling(newOrderNo);
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || '创建订单失败，请稍后重试';
      toast({
        variant: 'destructive',
        title: '创建订单失败',
        description: errorMessage,
      });
    } finally {
      setCreatingOrder(false);
    }
  };

  // 关闭支付对话框
  const handleClosePaymentDialog = () => {
    setPaymentDialogOpen(false);
    stopPolling();
  };

  const handleRedeemGiftCode = async () => {
    if (!isAuthenticated) {
      toast({
        variant: 'destructive',
        title: '请先登录',
        description: '使用邀测码需要先登录您的账号'
      });

      // 保存当前页面URL，登录后可以返回
      const returnUrl = encodeURIComponent(window.location.pathname);
      router.push(`/login?returnUrl=${returnUrl}`);
      return;
    }

    if (!giftCode.trim()) {
      toast({
        variant: 'destructive',
        title: '请输入邀测码',
      });
      return;
    }

    // 检查是否是自己的邀测码
    if (user?.id && isUserOwnInviteCode(user.id, giftCode.trim())) {
      toast({
        variant: 'destructive',
        title: '不能使用自己的邀测码',
        description: '请使用朋友分享给您的邀测码，或邀请朋友使用您的邀测码',
      });
      return;
    }

    setRedeeming(true);
    try {
      const result = await api.post('/api/gift-codes/redeem', { code: giftCode.trim() });
      const { courseId, course } = result.data;

      toast({
        title: '兑换成功',
        description: `您已成功获得课程《${course.title}》`,
      });

      setTimeout(() => {
        router.push(`/all-courses/${courseId}`);
      }, 1500);
    } catch (err: any) {
      toast({
        variant: 'destructive',
        title: '兑换失败',
        description: err.response?.data?.message || '请检查邀测码是否正确',
      });
    } finally {
      setRedeeming(false);
    }
  };

  let content;
  if (loading) {
    content = (
      <div className="flex justify-center items-center min-h-[400px]">
        <LoadingSpinner />
      </div>
    );
  } else if (error || !course) {
    content = (
      <div className="text-center text-red-500 min-h-[400px] flex items-center justify-center">
        {error || '课程不存在'}
      </div>
    );
  } else {
    content = (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-2xl mx-auto">
          <Card className="p-6">
            {!isClient ? (
              // 服务端渲染时显示占位内容
              <div className="min-h-[400px] flex justify-center items-center">
                <LoadingSpinner />
              </div>
            ) : (
              // 客户端渲染时显示完整内容
              <>
                <div className="mb-6">
                  <h2 className="text-2xl font-bold mb-2">{course.title}</h2>
                  <p className="text-muted-foreground">{course.description}</p>
                </div>

                <div className="flex items-center gap-6 mb-6 text-muted-foreground">
                  <Badge variant="secondary">{course.level}</Badge>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <span>{course.duration}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <BookOpen className="h-4 w-4" />
                    <span>{course.lessonsCount} 课时</span>
                  </div>
                </div>

                <div className="border-t pt-6">
                  {!isAuthenticated ? (
                    <div className="bg-blue-50 p-4 rounded-lg mb-6">
                      <h3 className="text-lg font-medium mb-2 text-blue-700">请先登录</h3>
                      <p className="text-blue-600 mb-4">使用邀测码需要先登录您的账号，以便我们将课程添加到您的学习列表中。</p>
                      <Button
                        asChild
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        <Link href={`/login?returnUrl=${encodeURIComponent(window.location.pathname)}`}>
                          立即登录
                        </Link>
                      </Button>
                    </div>
                  ) : (
                    <div className="mb-8">
                      <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
                        <div className="flex">
                          <AlertCircle className="h-6 w-6 text-yellow-500 mr-2 flex-shrink-0" />
                          <div>
                            <p className="text-sm font-medium text-yellow-700">邀测码使用规则</p>
                            <p className="text-sm text-yellow-600">
                              您<strong>不能使用自己生成的邀测码</strong>，需要使用朋友分享给您的邀测码。
                              如果您还没有邀测码，可以<Link href="/all-courses/invite-code" className="text-blue-600 hover:underline">点击这里</Link>生成邀测码分享给朋友，并获取朋友的邀测码。
                            </p>
                          </div>
                        </div>
                      </div>

                      <h3 className="text-lg font-medium mb-4">使用邀测码</h3>
                      <div className="flex gap-4">
                        <Input
                          placeholder="请输入朋友的邀测码"
                          value={giftCode}
                          onChange={handleGiftCodeChange}
                          className={`flex-1 ${isOwnCode ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
                        />
                        <Button
                          onClick={handleRedeemGiftCode}
                          disabled={redeeming || isOwnCode}
                          className="bg-red-500 hover:bg-red-600 text-white"
                        >
                          {redeeming ? (
                            <>
                              <LoadingSpinner className="mr-2 h-4 w-4" />
                              兑换中...
                            </>
                          ) : (
                            '兑换'
                          )}
                        </Button>
                      </div>

                      {isOwnCode && (
                        <p className="text-red-500 text-sm mt-2">
                          这是您自己生成的邀测码，您不能使用自己的邀测码。请使用朋友分享给您的邀测码。
                        </p>
                      )}
                    </div>
                  )}

                  <div className="flex items-center justify-between mb-6">
                    <span className="text-lg font-medium">课程价格</span>
                    <span className="text-2xl font-bold text-blue-500">${course.price}</span>
                  </div>

                  <div className="flex flex-col gap-4">
                    <Button
                      onClick={handlePurchase}
                      disabled={creatingOrder}
                      className="w-full bg-blue-500 hover:bg-blue-600 text-white"
                      size="lg"
                    >
                      {creatingOrder ? (
                        <>
                          <LoadingSpinner className="mr-2 h-4 w-4" />
                          处理中...
                        </>
                      ) : (
                        '立即购买'
                      )}
                    </Button>

                    <Button
                      asChild
                      className="w-full border-2 border-blue-500 bg-transparent hover:bg-blue-50 text-blue-500"
                      size="lg"
                    >
                      <Link href={`/all-courses/invite-code?courseId=${courseId}&courseName=${encodeURIComponent(course.title)}`}>
                        获取邀测码
                      </Link>
                    </Button>
                  </div>
                </div>
              </>
            )}
          </Card>
        </div>
      </div>
    );
  }

  return (
    <>
      {content}

      {/* Stripe支付对话框 */}
      <StripePaymentDialog
        open={paymentDialogOpen}
        onOpenChange={handleClosePaymentDialog}
        clientSecret={clientSecret || undefined}
        orderNo={orderNo || undefined}
        amount={course ? course.price : 0} // 直接使用课程价格，不乘以100
        isLoading={!clientSecret && paymentDialogOpen}
        onCancel={handleClosePaymentDialog}
        onSuccess={() => {
          setPaymentDialogOpen(false);
          toast({
            title: '支付成功',
            description: `您已成功购买课程《${course?.title}》`,
          });
          setTimeout(() => {
            router.push(`/my-courses/${courseId}`);
          }, 1500);
        }}
        onError={(error) => {
          toast({
            variant: 'destructive',
            title: '支付失败',
            description: error || '请稍后重试',
          });
        }}
      />
    </>
  );
}