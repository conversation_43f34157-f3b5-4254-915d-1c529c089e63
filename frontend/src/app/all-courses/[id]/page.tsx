import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Course, CourseLesson, CourseService } from '@/lib/services/course.service';
import { Clock, BookOpen } from 'lucide-react';
import Link from 'next/link';
import { notFound } from 'next/navigation';

interface CoursePageProps {
  params: { id: string };
}

// 動態設置 meta 標籤
export async function generateMetadata({ params }: CoursePageProps) {
  // 先检查ID格式，如果不是有效的UUID就返回默认metadata
  if (!isValidUUID(params.id)) {
    return {
      title: '課程不存在',
      description: '找不到該課程',
    };
  }

  try {
    const course = await CourseService.getCourse(params.id);
    return {
      title: course.title,
      description: course.description,
      openGraph: {
        title: course.title,
        description: course.description,
        // 可根據需要添加圖片等
      },
    };
  } catch {
    return {
      title: '課程不存在',
      description: '找不到該課程',
    };
  }
}

// UUID 验证函数
function isValidUUID(str: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(str);
}

export default async function CoursePage({ params }: CoursePageProps) {
  // 先检查ID格式，如果不是有效的UUID就直接返回404
  if (!isValidUUID(params.id)) {
    notFound();
  }

  let course: Course | null = null;
  let lessons: CourseLesson[] = [];
  let error = null;
  try {
    course = await CourseService.getCourse(params.id);
    lessons = await CourseService.getCoursePublicLessons(params.id);
  } catch (err) {
    error = '加载课程信息失败，请稍后重试';
  }

  if (error || !course) {
    // SSR下直接返回404
    notFound();
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {/* 课程头部信息 */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Badge variant="secondary">{course.level}</Badge>
            <Badge variant="default" className={`${
              Number(course.price) === 0 
                ? "bg-green-500" 
                : "bg-blue-500"
            }`}>
              {Number(course.price) === 0 ? "免费" : `$${course.price}`}
            </Badge>
          </div>
          <h1 className="text-3xl font-bold mb-4">{course.title}</h1>
          <p className="text-lg text-muted-foreground mb-6">{course.description}</p>
          <div className="flex items-center gap-6 text-muted-foreground">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              <span>{course.duration}</span>
            </div>
            <div className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              <span>{course.lessonsCount} 课时</span>
            </div>
          </div>
        </div>

        {/* 购买按钮 */}
        <div className="mb-8">
          <Button 
            asChild 
            size="lg" 
            className={`w-full md:w-auto ${
              Number(course.price) === 0 
                ? "bg-green-600 hover:bg-green-700" 
                : ""
            }`}
          >
            <Link href={
              Number(course.price) === 0 
                ? `/all-courses/free-enroll/${course.id}` 
                : `/all-courses/purchase/${course.id}`
            }>
              {Number(course.price) === 0 ? "立即加入" : "开始学习"}
            </Link>
          </Button>
        </div>

        {/* 课程章节列表 */}
        <div className="space-y-4">
          <h2 className="text-2xl font-semibold mb-4">课程大纲</h2>
          {lessons.map((lesson) => (
            <Card key={lesson.id} className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className="text-muted-foreground">第 {lesson.order} 章</span>
                  </div>
                  <h3 className="text-lg font-medium mt-1">{lesson.title}</h3>
                  <p className="text-muted-foreground mt-1">{lesson.description}</p>
                  <div className="flex items-center gap-2 mt-2 text-sm text-muted-foreground">
                    <Clock className="h-4 w-4" />
                    <span>{lesson.duration}</span>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
} 