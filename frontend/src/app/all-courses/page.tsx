"use client";

import { useEffect, useState } from "react";
import { Course, CourseService } from '@/lib/services/course.service'
import { CourseCard } from '@/components/course/course-card'

interface Category {
  id: string;
  name: string;
  description?: string | null;
}

export default function CoursesPage() {
  const [courses, setCourses] = useState<(Course & { categoryId?: string; category?: Category })[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        const [coursesData, categoriesData] = await Promise.all([
          CourseService.getCourses(),
          CourseService.getCategories(),
        ]);
        setCourses(coursesData);
        setCategories(categoriesData);
      } catch (err: any) {
        if (err.response?.status === 401) {
          setError('請先登入後再查看課程');
        } else {
          setError('獲取課程失敗，請稍後再試');
        }
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  // 根據分類分組課程
  const coursesByCategory: Record<string, Course[]> = {};
  categories.forEach(cat => {
    coursesByCategory[cat.id] = courses.filter(course => {
      // course.category 可能是物件或id
      if (course.category && typeof course.category === 'object') {
        return course.category.id === cat.id;
      }
      // 若有categoryId欄位
      if ('categoryId' in course) {
        return course.categoryId === cat.id;
      }
      return false;
    });
  });

  if (loading) {
    return <div className="flex justify-center items-center min-h-[400px]">加載中...</div>;
  }

  if (error) {
    return (
      <div className="text-center text-red-500 min-h-[400px] flex items-center justify-center">
        {error}
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      {categories.map((cat) => (
        <section className="mb-12" key={cat.id}>
          <div className="flex items-center gap-4 mb-6">
            <h2 className="text-2xl font-semibold">{cat.name}</h2>
            <div className="flex-1 h-[1px] bg-border"></div>
          </div>
          {cat.description && (
            <div className="mb-4 text-slate-500 text-sm">{cat.description}</div>
          )}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {coursesByCategory[cat.id].length === 0 ? (
              <div className="col-span-3 text-slate-400 text-center py-8">該分類下暫無課程</div>
            ) : (
              coursesByCategory[cat.id].map((course) => (
                <CourseCard
                  key={course.id}
                  id={course.id}
                  title={course.title}
                  description={course.description}
                  duration={course.duration}
                  level={course.level}
                  lessonsCount={course.lessonsCount}
                  price={course.price}
                  isPurchased={course.isPurchased}
                />
              ))
            )}
          </div>
        </section>
      ))}
    </div>
  );
} 