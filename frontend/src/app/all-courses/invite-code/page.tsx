'use client';

import { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useToast } from '@/components/ui/use-toast';
import { api } from '@/lib/api';
import { X, Share2, Copy, AlertCircle, Download, Image } from 'lucide-react';
import { useAuth } from '@/auth/contexts/simplified-auth-context';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import html2canvas from 'html2canvas';

// 分享图片模态框组件
interface ShareModalProps {
  inviteCode: string;
  friendName: string;
  courseName: string;
  reason: string;
  userName: string;
  existingImageUrl?: string | null;
  onClose: () => void;
}

const ShareModal = ({ inviteCode, friendName, courseName, reason, userName, existingImageUrl, onClose }: ShareModalProps) => {
  const { toast } = useToast();
  const [imageUrl, setImageUrl] = useState<string | null>(existingImageUrl || null);
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);
  const invitationCardRef = useRef<HTMLDivElement>(null);
  
  const handleCopyCode = () => {
    navigator.clipboard.writeText(inviteCode);
    toast({
      title: "邀测码已复制",
      description: "邀测码已复制到剪贴板",
    });
  };

  // 复制邀请信内容
  const handleCopyInvitation = () => {
    const invitationText = `
亲爱的${friendName}：

我想邀请您一起和我来学习AI探索家的"${courseName}"课程。

${reason}

期待与您一起学习和成长！

${userName} 敬上
邀测码：${inviteCode}
    `;
    navigator.clipboard.writeText(invitationText.trim());
    toast({
      title: "邀请信已复制",
      description: "邀请信已复制到剪贴板，可以发送给朋友了",
    });
  };

  // 生成邀请信图片
  const generateInvitationImage = async () => {
    if (!invitationCardRef.current) return;
    
    setIsGeneratingImage(true);
    
    try {
      // 确保元素可见（但仍在视口外）
      const originalStyles = {
        position: invitationCardRef.current.style.position,
        left: invitationCardRef.current.style.left,
        top: invitationCardRef.current.style.top,
        opacity: invitationCardRef.current.style.opacity,
        zIndex: invitationCardRef.current.style.zIndex
      };
      
      // 临时调整样式以确保html2canvas可以正确渲染
      invitationCardRef.current.style.position = 'fixed';
      invitationCardRef.current.style.left = '0';
      invitationCardRef.current.style.top = '0';
      invitationCardRef.current.style.opacity = '1';
      invitationCardRef.current.style.zIndex = '-9999'; // 确保在其他元素下方
      
      const canvas = await html2canvas(invitationCardRef.current, {
        scale: 2, // 提高清晰度
        backgroundColor: '#ffffff',
        logging: false,
        useCORS: true
      });
      
      // 恢复原始样式
      invitationCardRef.current.style.position = originalStyles.position;
      invitationCardRef.current.style.left = originalStyles.left;
      invitationCardRef.current.style.top = originalStyles.top;
      invitationCardRef.current.style.opacity = originalStyles.opacity;
      invitationCardRef.current.style.zIndex = originalStyles.zIndex;
      
      const url = canvas.toDataURL('image/png');
      setImageUrl(url);
      
      toast({
        title: "邀请信图片已生成",
        description: "您可以保存图片并分享给朋友",
      });
    } catch (error) {
      console.error('生成图片失败:', error);
      toast({
        variant: 'destructive',
        title: "生成图片失败",
        description: "请稍后再试",
      });
    } finally {
      setIsGeneratingImage(false);
    }
  };

  // 下载邀请信图片
  const downloadInvitationImage = () => {
    if (!imageUrl) return;
    
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `邀请函-${friendName}-${inviteCode}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast({
      title: "邀请信图片已下载",
      description: "您可以将图片分享给朋友",
    });
  };

  return (
    <div 
      className="fixed inset-0 bg-black/70 flex items-center justify-center z-50"
      onClick={(e) => {
        if (e.target === e.currentTarget) onClose();
      }}
    >
      <div className="bg-white rounded-xl p-6 max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-bold">分享邀测码给朋友</h3>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={20} />
          </button>
        </div>
        
        {/* 邀请信图片预览 */}
        {imageUrl && (
          <div className="mb-6">
            <h4 className="font-medium text-gray-800 mb-2">邀请信图片</h4>
            <div className="border border-gray-200 rounded-lg p-2 bg-white">
              <img 
                src={imageUrl} 
                alt="邀请函" 
                className="w-full h-auto rounded-lg"
              />
            </div>
            <div className="grid grid-cols-2 gap-4 mt-4">
              <Button 
                variant="outline" 
                className="flex items-center justify-center gap-2"
                onClick={downloadInvitationImage}
              >
                <Download size={16} />
                下载图片
              </Button>
              <Button 
                className="bg-blue-600 hover:bg-blue-700 flex items-center justify-center gap-2"
                onClick={() => setImageUrl(null)}
              >
                <Image size={16} />
                重新生成
              </Button>
            </div>
          </div>
        )}
        
        {!imageUrl && (
          <>
            {/* 邀请信模板 - 用于生成图片 */}
            <div 
              ref={invitationCardRef} 
              className="border border-gray-200 rounded-lg p-6 mb-6 bg-white"
              style={{ width: '100%', maxWidth: '500px' }}
            >
              <div className="text-center mb-4">
                <div className="text-xl font-bold text-blue-600 mb-1">AI探索家</div>
                <div className="text-2xl font-bold mb-2">课程邀请函</div>
                <div className="w-16 h-1 bg-blue-500 mx-auto"></div>
              </div>
              
              <div className="space-y-4 text-gray-700">
                <p className="font-medium text-lg">亲爱的{friendName || "[朋友姓名]"}：</p>
                <p>我想邀请您一起和我来学习AI探索家的"{courseName || "[课程名称]"}"课程。</p>
                <p className="italic">{reason || "[学习原因]"}</p>
                <p>期待与您一起学习和成长！</p>
                <div className="text-right">
                  <p className="font-medium">{userName || "[您的姓名]"} 敬上</p>
                </div>
                
                <div className="mt-6 pt-4 border-t border-gray-200">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-gray-500">邀测码</p>
                      <p className="text-xl font-bold tracking-wider text-blue-600">{inviteCode}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-500">有效期</p>
                      <p className="text-sm">三个月</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* 邀请信部分 - 放在最前面，更加突出 */}
            <div className="border border-gray-200 rounded-lg p-4 mb-6 bg-white">
              <h4 className="font-medium text-gray-800 mb-2">个性化邀请信</h4>
              <div className="bg-gray-50 p-4 rounded-lg text-gray-700 mb-3 whitespace-pre-line">
                <p className="font-medium">亲爱的{friendName || "[朋友姓名]"}：</p>
                <p className="my-3">我想邀请您一起和我来学习AI探索家的"{courseName || "[课程名称]"}"课程。</p>
                <p className="my-3">{reason || "[学习原因]"}</p>
                <p className="my-3">期待与您一起学习和成长！</p>
                <p className="mt-4 font-medium">{userName || "[您的姓名]"} 敬上</p>
                <p className="mt-2 text-blue-600 font-medium">邀测码：{inviteCode}</p>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex items-center justify-center gap-2"
                  onClick={handleCopyInvitation}
                >
                  <Copy size={16} />
                  复制邀请信
                </Button>
                <Button 
                  size="sm" 
                  className="bg-blue-600 hover:bg-blue-700 flex items-center justify-center gap-2"
                  onClick={generateInvitationImage}
                  disabled={isGeneratingImage}
                >
                  {isGeneratingImage ? (
                    <>
                      <LoadingSpinner className="h-4 w-4 mr-2" />
                      生成中...
                    </>
                  ) : (
                    <>
                      <Image size={16} />
                      生成图片
                    </>
                  )}
                </Button>
              </div>
            </div>
            
            <div className="bg-blue-50 p-4 rounded-lg mb-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm text-blue-600 mb-1">您的专属邀测码</p>
                  <p className="text-2xl font-bold tracking-wider text-blue-700 select-all">{inviteCode}</p>
                </div>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  onClick={handleCopyCode}
                  className="text-blue-600 hover:text-blue-800 hover:bg-blue-100"
                  title="复制邀测码"
                >
                  <Copy size={20} />
                </Button>
              </div>
            </div>
            
            <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
              <div className="flex">
                <AlertCircle className="h-6 w-6 text-yellow-500 mr-2" />
                <div>
                  <p className="text-sm font-medium text-yellow-700">重要提示</p>
                  <p className="text-sm text-yellow-600">
                    您不能使用自己生成的邀测码，请将此码分享给朋友，并获取朋友的邀测码来解锁课程。
                  </p>
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4 mt-6">
              <Button 
                variant="outline" 
                className="flex items-center justify-center gap-2"
                onClick={handleCopyCode}
              >
                <Copy size={16} />
                复制邀测码
              </Button>
              <Button 
                className="bg-blue-600 hover:bg-blue-700 flex items-center justify-center gap-2"
                onClick={handleCopyInvitation}
              >
                <Share2 size={16} />
                分享邀请信
              </Button>
            </div>
          </>
        )}
        
        <p className="text-xs text-gray-500 text-center mt-4">
          点击空白处关闭
        </p>
      </div>
    </div>
  );
};

// 保存用户生成的邀测码到localStorage
const saveUserInviteCode = (userId: string, inviteCode: string) => {
  try {
    // 获取现有的邀测码记录
    const existingCodesStr = localStorage.getItem('userInviteCodes');
    const existingCodes = existingCodesStr ? JSON.parse(existingCodesStr) : {};
    
    // 添加或更新当前用户的邀测码
    existingCodes[userId] = inviteCode;
    
    // 保存回localStorage
    localStorage.setItem('userInviteCodes', JSON.stringify(existingCodes));
    
    // 同时保存一个反向映射，用于检查邀测码是谁生成的
    const codeToUserStr = localStorage.getItem('inviteCodeToUser');
    const codeToUser = codeToUserStr ? JSON.parse(codeToUserStr) : {};
    
    codeToUser[inviteCode] = userId;
    localStorage.setItem('inviteCodeToUser', JSON.stringify(codeToUser));
    
    console.log('邀测码已保存:', userId, inviteCode);
  } catch (error) {
    console.error('保存邀测码失败:', error);
  }
};

export default function InviteCodePage() {
  const { toast } = useToast();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [inviteCode, setInviteCode] = useState('');
  const [showShareModal, setShowShareModal] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);
  const invitationCardRef = useRef<HTMLDivElement>(null);
  const [courseId, setCourseId] = useState<string>('');
  
  // 表单状态
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [profession, setProfession] = useState('');
  const [aiExperience, setAiExperience] = useState('');
  const [interests, setInterests] = useState<string[]>([]);
  const [expectation, setExpectation] = useState('');
  const [agreeTerms, setAgreeTerms] = useState(false);
  const [friendName, setFriendName] = useState('');
  const [courseName, setCourseName] = useState('AI基础与应用');
  const [inviteReason, setInviteReason] = useState('');

  // 兴趣选项
  const interestOptions = [
    { id: 'ai-basics', label: 'AI基础知识' },
    { id: 'prompt-engineering', label: 'Prompt工程' },
    { id: 'llm-applications', label: 'LLM应用开发' },
    { id: 'ai-tools', label: 'AI工具使用' },
    { id: 'ai-business', label: 'AI商业应用' },
  ];

  useEffect(() => {
    setIsClient(true);
    
    // 如果已登录，自动填充用户信息
    if (isAuthenticated && user) {
      setName(user.name || '');
      setEmail(user.email || '');
    }

    // 从URL查询参数中获取courseId
    const courseIdFromUrl = searchParams.get('courseId');
    if (courseIdFromUrl && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(courseIdFromUrl)) {
      setCourseId(courseIdFromUrl);
      console.log('从URL查询参数获取到课程ID:', courseIdFromUrl);
    } else {
      // 如果URL中没有有效的课程ID，使用默认课程ID
      setCourseId('204bf0e0-4ead-49cc-bd5b-88376c388748');
      console.log('使用默认课程ID');
    }

    // 从URL查询参数中获取courseName
    const courseNameFromUrl = searchParams.get('courseName');
    if (courseNameFromUrl) {
      setCourseName(courseNameFromUrl);
      console.log('从URL查询参数获取到课程名称:', courseNameFromUrl);
    } else {
      // 如果URL中没有有效的课程名称，使用默认课程名称
      setCourseName('AI基础与应用');
      console.log('使用默认课程名称');
    }
  }, [isAuthenticated, user, searchParams]);

  const handleInterestChange = (id: string) => {
    setInterests(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isAuthenticated) {
      toast({
        variant: 'destructive',
        title: '请先登录',
        description: '获取邀测码需要先登录您的账号'
      });
      
      // 保存当前页面URL，登录后可以返回
      const returnUrl = encodeURIComponent(window.location.pathname);
      router.push(`/login?returnUrl=${returnUrl}`);
      return;
    }
    
    // 表单验证
    if (!name || !email || !profession || !aiExperience || interests.length === 0 || !expectation || !agreeTerms) {
      toast({
        variant: 'destructive',
        title: '请完成所有必填项',
        description: '请确保您已填写所有必填信息并同意条款',
      });
      return;
    }

    // 验证课程ID
    if (!courseId) {
      toast({
        variant: 'destructive',
        title: '课程ID无效',
        description: '无法获取课程信息，请返回课程页面重试',
      });
      return;
    }

    // 邀请信信息验证 - 这些是可选的，不强制要求填写
    if (!friendName) {
      setFriendName('朋友');  // 设置默认值
    }
    
    if (!inviteReason) {
      setInviteReason('我觉得这门课程非常有价值，希望我们能一起学习，共同进步。');  // 设置默认值
    }

    setIsSubmitting(true);

    try {
      // 调用真实API生成邀请码
      const response = await api.post('/api/invite-codes/generate', {
        courseId, // 添加课程ID
        name, 
        email, 
        profession, 
        aiExperience, 
        interests, 
        expectation,
        friendName, 
        courseName, 
        inviteReason // 添加邀请信信息
      });
      
      // 获取API返回的邀请码
      const generatedCode = response.data.inviteCode;
      setInviteCode(generatedCode);
      
      // 保存用户生成的邀测码
      if (user?.id) {
        saveUserInviteCode(user.id, generatedCode);
        
        // 保存邀请信信息到localStorage
        try {
          const invitationInfo = {
            inviteCode: generatedCode,
            friendName,
            courseName,
            reason: inviteReason,
            userName: name
          };
          localStorage.setItem(`invitation_${generatedCode}`, JSON.stringify(invitationInfo));
        } catch (error) {
          console.error('保存邀请信信息失败:', error);
        }
      }
      
      setIsSuccess(true);
      
      toast({
        title: '邀测码生成成功',
        description: '感谢您的参与，您的邀测码已生成，请分享给朋友使用',
      });
    } catch (error) {
      console.error('生成邀测码失败:', error);
      
      // 添加错误处理的回退机制，确保用户体验
      // 如果API调用失败，使用本地生成的邀请码作为备用
      const fallbackCode = `INV-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
      setInviteCode(fallbackCode);
      
      if (user?.id) {
        saveUserInviteCode(user.id, fallbackCode);
        
        try {
          const invitationInfo = {
            inviteCode: fallbackCode,
            friendName,
            courseName,
            reason: inviteReason,
            userName: name
          };
          localStorage.setItem(`invitation_${fallbackCode}`, JSON.stringify(invitationInfo));
        } catch (error) {
          console.error('保存邀请信信息失败:', error);
        }
      }
      
      setIsSuccess(true);
      
      toast({
        variant: 'warning',
        title: '邀测码生成成功（离线模式）',
        description: '服务器连接异常，已生成临时邀测码，请稍后重试',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCopyInviteCode = () => {
    if (inviteCode) {
      navigator.clipboard.writeText(inviteCode);
      toast({
        title: "邀测码已复制",
        description: "邀测码已复制到剪贴板，可以分享给朋友了",
      });
    }
  };

  // 生成邀请信图片
  const generateInvitationImage = async () => {
    if (!invitationCardRef.current) return;
    
    setIsGeneratingImage(true);
    
    try {
      // 确保元素可见（但仍在视口外）
      const originalStyles = {
        position: invitationCardRef.current.style.position,
        left: invitationCardRef.current.style.left,
        top: invitationCardRef.current.style.top,
        opacity: invitationCardRef.current.style.opacity,
        zIndex: invitationCardRef.current.style.zIndex
      };
      
      // 临时调整样式以确保html2canvas可以正确渲染
      invitationCardRef.current.style.position = 'fixed';
      invitationCardRef.current.style.left = '0';
      invitationCardRef.current.style.top = '0';
      invitationCardRef.current.style.opacity = '1';
      invitationCardRef.current.style.zIndex = '-9999'; // 确保在其他元素下方
      
      const canvas = await html2canvas(invitationCardRef.current, {
        scale: 2, // 提高清晰度
        backgroundColor: '#ffffff',
        logging: false,
        useCORS: true
      });
      
      // 恢复原始样式
      invitationCardRef.current.style.position = originalStyles.position;
      invitationCardRef.current.style.left = originalStyles.left;
      invitationCardRef.current.style.top = originalStyles.top;
      invitationCardRef.current.style.opacity = originalStyles.opacity;
      invitationCardRef.current.style.zIndex = originalStyles.zIndex;
      
      const url = canvas.toDataURL('image/png');
      setImageUrl(url);
      
      toast({
        title: "邀请信图片已生成",
        description: "您可以保存图片并分享给朋友",
      });
    } catch (error) {
      console.error('生成图片失败:', error);
      toast({
        variant: 'destructive',
        title: "生成图片失败",
        description: "请稍后再试",
      });
    } finally {
      setIsGeneratingImage(false);
    }
  };

  // 下载邀请信图片
  const downloadInvitationImage = () => {
    if (!imageUrl) return;
    
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `邀请函-${friendName}-${inviteCode}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast({
      title: "邀请信图片已下载",
      description: "您可以将图片分享给朋友",
    });
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-3xl mx-auto">
        {!isClient ? (
          // 服务端渲染时显示占位内容
          <div className="min-h-[400px] flex justify-center items-center">
            <LoadingSpinner />
          </div>
        ) : !isAuthenticated ? (
          // 未登录状态
          <Card>
            <CardHeader>
              <CardTitle className="text-center">获取邀测码</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <div className="bg-blue-50 p-6 rounded-lg mb-6">
                <p className="text-lg font-medium text-blue-700 mb-4">请先登录您的账号</p>
                <p className="text-muted-foreground mb-6">
                  获取邀测码需要先登录您的账号，以便我们能够为您提供个性化的学习体验
                </p>
                <Button 
                  asChild
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Link href={`/login?returnUrl=${encodeURIComponent(window.location.pathname)}`}>
                    立即登录
                  </Link>
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                还没有账号？<Link href="/register" className="text-blue-600 hover:underline">立即注册</Link>
              </p>
            </CardContent>
          </Card>
        ) : !isSuccess ? (
          <Card>
            <CardHeader>
              <CardTitle className="text-center">获取邀测码</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                <div className="flex">
                  <AlertCircle className="h-6 w-6 text-yellow-500 mr-2 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-yellow-700">邀请规则</p>
                    <p className="text-sm text-yellow-600">
                      您生成的邀测码<strong>只能给朋友使用</strong>，您需要获取朋友的邀测码才能解锁课程。这样可以促进社区互动和知识分享。
                    </p>
                  </div>
                </div>
              </div>
              
              <p className="text-muted-foreground mb-6 text-center">
                请填写以下信息，帮助我们了解您的学习需求，我们将为您生成专属邀测码
              </p>
              
              <form onSubmit={handleSubmit}>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="name">姓名</Label>
                      <Input 
                        id="name" 
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        placeholder="请输入您的姓名" 
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="email">邮箱</Label>
                      <Input 
                        id="email" 
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="请输入您的邮箱" 
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="profession">您的职业</Label>
                    <Input 
                      id="profession" 
                      value={profession}
                      onChange={(e) => setProfession(e.target.value)}
                      placeholder="例如：软件工程师、产品经理、学生等" 
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label>您的AI经验水平</Label>
                    <RadioGroup value={aiExperience} onValueChange={setAiExperience}>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="beginner" id="beginner" />
                        <Label htmlFor="beginner">初学者（刚接触AI）</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="intermediate" id="intermediate" />
                        <Label htmlFor="intermediate">中级（有一定了解和使用经验）</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="advanced" id="advanced" />
                        <Label htmlFor="advanced">高级（经常使用AI工具或有开发经验）</Label>
                      </div>
                    </RadioGroup>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>您对哪些AI主题感兴趣？（可多选）</Label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
                      {interestOptions.map((option) => (
                        <div key={option.id} className="flex items-center space-x-2">
                          <Checkbox 
                            id={option.id} 
                            checked={interests.includes(option.id)}
                            onCheckedChange={() => handleInterestChange(option.id)}
                          />
                          <Label htmlFor={option.id}>{option.label}</Label>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="expectation">您对本课程的期望</Label>
                    <Textarea 
                      id="expectation" 
                      value={expectation}
                      onChange={(e) => setExpectation(e.target.value)}
                      placeholder="请简要描述您希望从本课程中学到什么" 
                      rows={4}
                    />
                  </div>
                  
                  {/* 邀请信相关字段 */}
                  <div className="space-y-2 border-t pt-4 mt-4">
                    <h3 className="font-medium text-gray-800 mb-2">邀请信信息（用于分享给朋友）</h3>
                    
                    <div className="space-y-2">
                      <Label htmlFor="friendName">朋友姓名</Label>
                      <Input 
                        id="friendName" 
                        value={friendName}
                        onChange={(e) => setFriendName(e.target.value)}
                        placeholder="请输入您想邀请的朋友姓名" 
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="courseName">课程名称</Label>
                      <Input 
                        id="courseName" 
                        value={courseName}
                        onChange={(e) => setCourseName(e.target.value)}
                        placeholder="请输入您想学习的课程名称" 
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="inviteReason">邀请原因</Label>
                      <Textarea 
                        id="inviteReason" 
                        value={inviteReason}
                        onChange={(e) => setInviteReason(e.target.value)}
                        placeholder="请简要描述为什么想邀请朋友一起学习这门课程" 
                        rows={3}
                      />
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="terms" 
                      checked={agreeTerms}
                      onCheckedChange={(checked) => setAgreeTerms(checked === true)}
                    />
                    <Label htmlFor="terms">
                      我同意收集我的信息用于改进课程，并接收相关更新
                    </Label>
                  </div>
                </div>
                
                <Button 
                  type="submit" 
                  className="w-full mt-6" 
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <LoadingSpinner className="mr-2 h-4 w-4" />
                      提交中...
                    </>
                  ) : (
                    '提交获取邀测码'
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle className="text-center">恭喜您获得邀测码！</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <div className="bg-blue-50 p-6 rounded-lg mb-6">
                <p className="text-sm text-blue-600 mb-2">您的专属邀测码</p>
                <div className="flex items-center justify-center gap-2">
                  <p className="text-3xl font-bold tracking-wider text-blue-700 select-all">{inviteCode}</p>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    onClick={handleCopyInviteCode}
                    className="text-blue-600 hover:text-blue-800 hover:bg-blue-100"
                    title="复制邀测码"
                  >
                    <Copy size={20} />
                  </Button>
                </div>
              </div>
              
              <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6 text-left">
                <div className="flex">
                  <AlertCircle className="h-6 w-6 text-yellow-500 mr-2 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-yellow-700">重要提示</p>
                    <p className="text-sm text-yellow-600">
                      您<strong>不能使用自己生成的邀测码</strong>。请将此码分享给朋友，并获取朋友的邀测码来解锁课程。
                    </p>
                  </div>
                </div>
              </div>
              
              {/* 邀请信图片预览 */}
              {imageUrl ? (
                <div className="border border-gray-200 rounded-lg p-4 mb-6 bg-white">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-medium text-gray-800">邀请函图片</h4>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="text-blue-600 hover:text-blue-800 hover:bg-blue-100"
                      onClick={() => setImageUrl(null)}
                    >
                      重新生成
                    </Button>
                  </div>
                  <div className="bg-white p-2 rounded-lg mb-3">
                    <img 
                      src={imageUrl} 
                      alt="邀请函" 
                      className="w-full h-auto rounded-lg"
                    />
                  </div>
                  <Button 
                    variant="outline" 
                    className="w-full flex items-center justify-center gap-2"
                    onClick={downloadInvitationImage}
                  >
                    <Download size={16} />
                    下载邀请函图片
                  </Button>
                </div>
              ) : (
                <>
                  {/* 邀请信预览 */}
                  <div className="border border-gray-200 rounded-lg p-4 mb-6 bg-white text-left">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-medium text-gray-800">您的邀请信</h4>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        className="text-blue-600 hover:text-blue-800 hover:bg-blue-100"
                        onClick={() => setShowShareModal(true)}
                      >
                        查看完整邀请信
                      </Button>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg text-gray-700 mb-3">
                      <p className="font-medium">亲爱的{friendName}：</p>
                      <p className="my-2 line-clamp-2">我想邀请您一起和我来学习AI探索家的"{courseName}"课程。</p>
                      <p className="line-clamp-2 text-gray-500">{inviteReason || "..."}</p>
                      <p className="mt-2 text-xs text-gray-400">点击"查看完整邀请信"以查看全部内容</p>
                    </div>
                  </div>
                  
                  {/* 隐藏的邀请信模板 - 用于生成图片 */}
                  <div 
                    ref={invitationCardRef} 
                    style={{ position: 'fixed', left: '-9999px', top: '-9999px', opacity: 0, pointerEvents: 'none', width: '500px' }}
                  >
                    <div className="bg-white p-6 rounded-lg" style={{ width: '500px', height: 'auto' }}>
                      <div className="text-center mb-4">
                        <div className="text-xl font-bold text-blue-600 mb-1">AI探索家</div>
                        <div className="text-2xl font-bold mb-2">课程邀请函</div>
                        <div className="w-16 h-1 bg-blue-500 mx-auto"></div>
                      </div>
                      
                      <div className="space-y-4 text-gray-700">
                        <p className="font-medium text-lg">亲爱的{friendName || "[朋友姓名]"}：</p>
                        <p>我想邀请您一起和我来学习AI探索家的"{courseName || "[课程名称]"}"课程。</p>
                        <p className="italic">{inviteReason || "[学习原因]"}</p>
                        <p>期待与您一起学习和成长！</p>
                        <div className="text-right">
                          <p className="font-medium">{name || "[您的姓名]"} 敬上</p>
                        </div>
                        
                        <div className="mt-6 pt-4 border-t border-gray-200">
                          <div className="flex justify-between items-center">
                            <div>
                              <p className="text-sm text-gray-500">邀测码</p>
                              <p className="text-xl font-bold tracking-wider text-blue-600">{inviteCode}</p>
                            </div>
                            <div className="text-right">
                              <p className="text-sm text-gray-500">有效期</p>
                              <p className="text-sm">三个月</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}
              
              <p className="text-muted-foreground mb-4">
                请将此邀请信分享给朋友，邀请他们加入学习。您需要使用朋友的邀测码才能解锁课程内容。
              </p>
            </CardContent>
            <CardFooter className="flex flex-col gap-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full">
                {!imageUrl && (
                  <Button 
                    className="bg-green-600 hover:bg-green-700 flex items-center justify-center gap-2"
                    onClick={generateInvitationImage}
                    disabled={isGeneratingImage}
                  >
                    {isGeneratingImage ? (
                      <>
                        <LoadingSpinner className="h-4 w-4 mr-2" />
                        生成中...
                      </>
                    ) : (
                      <>
                        <Image className="h-4 w-4" />
                        生成邀请函图片
                      </>
                    )}
                  </Button>
                )}
                <Button 
                  variant="outline" 
                  className="border-blue-500 text-blue-600 hover:bg-blue-50 flex items-center justify-center gap-2"
                  onClick={handleCopyInviteCode}
                >
                  <Copy className="h-4 w-4" />
                  复制邀测码
                </Button>
              </div>
              
              <Button 
                variant="link" 
                asChild
                className="text-gray-500 mt-2"
              >
                <Link href="/all-courses">
                  浏览所有课程
                </Link>
              </Button>
            </CardFooter>
          </Card>
        )}
        
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-2">邀测说明</h3>
          <ul className="list-disc list-inside space-y-2 text-gray-700">
            <li>邀测码有效期为三个月</li>
            <li>每个邀测码仅可兑换一次</li>
            <li><strong>您不能使用自己生成的邀测码</strong>，需要朋友的邀测码</li>
            <li>邀请朋友使用您的邀测码，共同学习效果更好</li>
            <li>邀测期间可能会有功能调整</li>
            <li>如遇问题请联系客服</li>
          </ul>
        </div>
      </div>
      
      {/* 分享模态框 */}
      {showShareModal && (
        <ShareModal 
          inviteCode={inviteCode} 
          friendName={friendName}
          courseName={courseName}
          reason={inviteReason}
          userName={name}
          existingImageUrl={imageUrl}
          onClose={() => setShowShareModal(false)} 
        />
      )}
    </div>
  );
} 