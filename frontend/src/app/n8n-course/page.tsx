import { Metadata } from 'next'
import { Settings, Clock, Target, Trophy, Users, Zap, Workflow, Home, ArrowLeft, ArrowRight, Play, CheckCircle } from 'lucide-react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

// n8n課程步驟數據
const courseSteps = [
  {
    id: 1,
    title: "n8n基礎介紹：什麼是自動化工作流？",
    type: "video",
    duration: "8分鐘",
    content: {
      description: "了解n8n是什麼，為什麼選擇n8n，以及它如何幫助你實現工作流自動化。",
      keyPoints: [
        "n8n的核心概念和特點",
        "開源vs商業化自動化工具對比",
        "n8n在實際工作中的應用場景"
      ]
    }
  },
  {
    id: 2,
    title: "n8n界面操作：掌握基本操作技巧",
    type: "interactive",
    duration: "10分鐘",
    content: {
      description: "通過實際演示學習n8n的界面操作，掌握創建和編輯工作流的基本技能。",
      principles: [
        {
          title: "節點(Node)概念",
          description: "每個節點代表一個操作或服務，通過連接節點來構建自動化流程",
          example: "HTTP請求節點 → 數據處理節點 → 發送郵件節點"
        },
        {
          title: "連接和數據傳遞",
          description: "理解節點之間的數據流動，學會配置輸入輸出參數",
          example: "前一個節點的輸出會自動成為下一個節點的輸入"
        },
        {
          title: "觸發器設置",
          description: "設置工作流的啟動條件，可以是定時、Webhook或手動觸發",
          example: "每天上午9點自動執行，或通過API調用觸發"
        }
      ]
    }
  },
  {
    id: 3,
    title: "實戰案例：構建第一個自動化工作流",
    type: "practice",
    duration: "15分鐘",
    content: {
      description: "通過三個實用的自動化案例，學會如何使用n8n解決實際工作問題。",
      cases: [
        {
          industry: "數據同步",
          company: "Google Sheets → Notion",
          highlights: ["自動數據導入", "格式轉換處理", "錯誤處理機制"]
        },
        {
          industry: "通知自動化",
          company: "郵件 → Slack 通知",
          highlights: ["郵件內容解析", "條件判斷邏輯", "多渠道通知"]
        },
        {
          industry: "API集成",
          company: "天氣API → 每日推送",
          highlights: ["外部API調用", "數據格式化", "定時任務設置"]
        }
      ]
    }
  },
  {
    id: 4,
    title: "進階技巧：複雜工作流設計",
    type: "tools",
    duration: "8分鐘",
    content: {
      description: "學習n8n的高級功能，包括條件分支、循環處理、錯誤處理等進階技巧。",
      tools: [
        {
          name: "條件分支(IF節點)",
          type: "邏輯控制",
          price: "免費功能",
          features: ["多條件判斷", "複雜邏輯組合", "動態路由"],
          suitable: "需要智能決策的工作流"
        },
        {
          name: "循環處理(Loop節點)",
          type: "批量操作",
          price: "免費功能",
          features: ["批量數據處理", "迭代操作", "性能優化"],
          suitable: "大量數據處理場景"
        },
        {
          name: "錯誤處理(Error Trigger)",
          type: "穩定性保障",
          price: "免費功能",
          features: ["異常捕獲", "重試機制", "報警通知"],
          suitable: "生產環境的可靠性"
        }
      ]
    }
  },
  {
    id: 5,
    title: "部署與優化：讓你的自動化工作流穩定運行",
    type: "summary",
    duration: "6分鐘",
    content: {
      description: "學習如何部署n8n工作流到生產環境，以及性能優化和監控的最佳實踐。",
      actionPlan: [
        "選擇合適的部署方式（雲端vs自架）",
        "配置工作流的監控和日誌",
        "設置錯誤通知和備份策略",
        "優化工作流性能和資源消耗",
        "建立工作流文檔和維護流程"
      ],
      nextSteps: "完成這個免費課程後，你可以繼續探索n8n的更多高級功能，或者查看我們的其他自動化工具課程。"
    }
  }
]

// 生成结构化数据
export function generateJSONLD() {
  return {
    "@context": "https://schema.org",
    "@type": "Course",
    "name": "n8n自動化工具免費入門課程",
    "description": "45分鐘免費學會n8n自動化工具，無需登錄即可開始學習",
    "provider": {
      "@type": "Organization",
      "name": "AI探索家"
    },
    "educationalLevel": "Beginner",
    "courseMode": "online",
    "isAccessibleForFree": true,
    "hasCourseInstance": {
      "@type": "CourseInstance",
      "courseMode": "online",
      "duration": "PT45M"
    },
    "about": [
      "n8n自動化",
      "工作流自動化", 
      "開源自動化工具",
      "NoCode自動化"
    ],
    "syllabus": courseSteps.map(step => ({
      "@type": "Syllabus",
      "name": step.title,
      "description": step.content.description,
      "timeRequired": step.duration
    }))
  }
}

// n8n課程專用導航組件
function N8nCourseNav() {
  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="max-w-4xl mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/" className="flex items-center text-gray-600 hover:text-gray-900">
              <ArrowLeft className="h-4 w-4 mr-1" />
              返回首頁
            </Link>
            <div className="h-4 w-px bg-gray-300"></div>
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-green-600" />
              <span className="font-medium text-gray-900">n8n免費入門課程</span>
            </div>
          </div>
                     <div className="flex items-center space-x-3">
             <Link href="/n8n-course/interactive" className="text-gray-600 hover:text-gray-900">
               互動版本
             </Link>
             <Link href="/n8n" className="flex items-center text-gray-600 hover:text-gray-900">
               <Home className="h-4 w-4 mr-1" />
               n8n專題首頁
             </Link>
           </div>
        </div>
      </div>
    </nav>
  )
}

// SEO优化：所有内容静态渲染，便于Google索引
export default function N8nCoursePage() {
  const jsonLD = generateJSONLD()
  
  return (
    <>
      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLD) }}
      />
      
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50">
        {/* 導航欄 */}
        <N8nCourseNav />
        
        {/* 課程頭部 - SEO優化的標題結構 */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-4xl mx-auto px-4 py-6">
            <div className="text-center">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                n8n自動化工具免費入門課程
              </h1>
              <p className="text-lg text-gray-600 mb-6">
                45分鐘免費學習 • 無需登錄 • 5個實用章節
              </p>
              <div className="flex justify-center items-center space-x-6 text-gray-600">
                <div className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  <span>45分鐘完成</span>
                </div>
                <div className="flex items-center">
                  <Trophy className="h-5 w-5 mr-2" />
                  <span>5個章節</span>
                </div>
                <div className="flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  <span>免費開放</span>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* 主要內容區域 - 所有內容靜態渲染便於SEO */}
        <main className="max-w-4xl mx-auto px-4 py-8">
          {/* 課程簡介 */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">課程內容概覽</h2>
            <div className="bg-white rounded-lg shadow-lg p-6">
              <p className="text-gray-700 text-lg leading-relaxed mb-4">
                本課程將帶你從零開始學習n8n自動化工具，涵蓋基礎概念、界面操作、實戰案例、進階技巧到部署優化等5個完整章節。
                無需任何編程基礎，45分鐘即可掌握工作流自動化的核心技能。
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <Settings className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-green-800">開源免費</h3>
                  <p className="text-sm text-green-600">完全開源，無需付費</p>
                </div>
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <Zap className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-blue-800">快速上手</h3>
                  <p className="text-sm text-blue-600">45分鐘完整掌握</p>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <Workflow className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-purple-800">實戰導向</h3>
                  <p className="text-sm text-purple-600">包含真實應用案例</p>
                </div>
              </div>
            </div>
          </section>

          {/* 詳細課程大綱 - 每個章節的完整內容都靜態渲染 */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">詳細課程大綱</h2>
            <div className="space-y-6">
              {courseSteps.map((step, index) => (
                <article key={step.id} className="bg-white rounded-lg shadow-lg overflow-hidden">
                  <div className="bg-green-50 px-6 py-4 border-b">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-xl font-bold text-gray-900">
                          第 {step.id} 章：{step.title}
                        </h3>
                        <div className="flex items-center mt-2 text-gray-600">
                          <Clock className="h-4 w-4 mr-1" />
                          <span>{step.duration}</span>
                          <Badge className="ml-3">
                            {step.type === 'video' ? '視頻教學' :
                             step.type === 'interactive' ? '互動練習' :
                             step.type === 'practice' ? '實戰案例' :
                             step.type === 'tools' ? '工具介紹' : '總結應用'}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-6">
                    <p className="text-gray-700 text-lg mb-6">{step.content.description}</p>
                    
                    {/* 第1章：要點列表 */}
                    {step.content.keyPoints && (
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-3">本章要點：</h4>
                        <ul className="space-y-2">
                          {step.content.keyPoints.map((point, pointIndex) => (
                            <li key={pointIndex} className="flex items-start">
                              <Target className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                              <span className="text-gray-700">{point}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                    
                    {/* 第2章：操作原理 */}
                    {step.content.principles && (
                      <div className="space-y-4">
                        {step.content.principles.map((principle, pIndex) => (
                          <div key={pIndex} className="border-l-4 border-green-500 pl-4">
                            <h4 className="font-bold text-lg mb-2">{principle.title}</h4>
                            <p className="text-gray-700 mb-3">{principle.description}</p>
                            <div className="bg-green-50 p-3 rounded">
                              <span className="text-sm font-medium text-green-800">示例：</span>
                              <span className="text-green-700 ml-1">{principle.example}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                    
                    {/* 第3章：實戰案例 */}
                    {step.content.cases && (
                      <div className="space-y-4">
                        {step.content.cases.map((caseItem, cIndex) => (
                          <div key={cIndex} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex items-center mb-3">
                              <Badge variant="outline" className="mr-3">
                                {caseItem.industry}
                              </Badge>
                              <h4 className="font-bold text-lg">{caseItem.company}</h4>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                              {caseItem.highlights.map((highlight, hIndex) => (
                                <div key={hIndex} className="bg-green-50 p-3 rounded">
                                  <Workflow className="h-4 w-4 text-green-600 mb-1" />
                                  <p className="text-sm font-medium text-green-800">
                                    {highlight}
                                  </p>
                                </div>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                    
                    {/* 第4章：工具介紹 */}
                    {step.content.tools && (
                      <div className="space-y-4">
                        {step.content.tools.map((tool, tIndex) => (
                          <div key={tIndex} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex justify-between items-start mb-4">
                              <div>
                                <h4 className="font-bold text-lg">{tool.name}</h4>
                                <Badge variant="secondary" className="mt-1">
                                  {tool.type}
                                </Badge>
                              </div>
                              <span className="font-semibold text-green-600">{tool.price}</span>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <h5 className="font-medium mb-2">主要功能：</h5>
                                <ul className="text-sm space-y-1">
                                  {tool.features.map((feature, fIndex) => (
                                    <li key={fIndex} className="flex items-center">
                                      <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                                      {feature}
                                    </li>
                                  ))}
                                </ul>
                              </div>
                              <div>
                                <h5 className="font-medium mb-2">適用場景：</h5>
                                <p className="text-sm text-gray-600">{tool.suitable}</p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                    
                    {/* 第5章：行動計劃 */}
                    {step.content.actionPlan && (
                      <div>
                        <div className="bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-lg mb-4">
                          <h4 className="font-bold text-lg mb-4 text-green-900">
                            你的n8n學習行動計劃
                          </h4>
                          <ol className="space-y-3">
                            {step.content.actionPlan.map((action, aIndex) => (
                              <li key={aIndex} className="flex items-start">
                                <span className="flex-shrink-0 w-6 h-6 bg-green-600 text-white text-sm font-bold rounded-full flex items-center justify-center mr-3 mt-0.5">
                                  {aIndex + 1}
                                </span>
                                <span className="text-green-800">{action}</span>
                              </li>
                            ))}
                          </ol>
                        </div>
                        {step.content.nextSteps && (
                          <div className="border-l-4 border-green-500 pl-4">
                            <h4 className="font-bold text-lg mb-3 text-green-800">
                              繼續探索n8n
                            </h4>
                            <p className="text-gray-700 mb-4">
                              {step.content.nextSteps}
                            </p>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </article>
              ))}
            </div>
          </section>
          
          {/* 開始學習按鈕 */}
          <section className="mt-12 text-center">
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                準備好開始學習了嗎？
              </h2>
              <p className="text-gray-600 mb-6">
                點擊下方按鈕，開始你的n8n自動化之旅
              </p>
              <div className="space-y-4">
                <Link href="/interactive-course" className="inline-block">
                  <Button size="lg" className="bg-green-600 hover:bg-green-700 px-8 py-3 text-lg">
                    開始免費學習
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <div className="flex justify-center items-center space-x-6 text-sm text-gray-600">
                  <span>✓ 完全免費</span>
                  <span>✓ 無需註冊</span>
                  <span>✓ 立即開始</span>
                </div>
              </div>
            </div>
          </section>
        </main>
      </div>
    </>
  )
} 