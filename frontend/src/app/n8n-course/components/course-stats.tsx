'use client'

import { useEffect, useState } from 'react'
import { Users } from 'lucide-react'

// 直接在组件内部定义简单的统计逻辑，避免导入问题
function formatNumber(num: number): string {
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

function getCourseStats() {
  const baseStats = 2847
  
  if (typeof window === 'undefined') {
    return { totalStarted: baseStats, totalCompleted: 1923 }
  }
  
  try {
    const saved = localStorage.getItem('course-local-stats')
    const localStats = saved ? JSON.parse(saved) : { started: 0, completed: 0 }
    return {
      totalStarted: baseStats + localStats.started,
      totalCompleted: 1923 + localStats.completed
    }
  } catch {
    return { totalStarted: baseStats, totalCompleted: 1923 }
  }
}

export function CourseStats() {
  const [stats, setStats] = useState<{ totalStarted: number; totalCompleted: number } | null>(null)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    try {
      setStats(getCourseStats())
    } catch (error) {
      console.error('Failed to load course stats:', error)
      setStats({ totalStarted: 2847, totalCompleted: 1923 })
    }
  }, [])

  // 避免水合错误：在客户端挂载之前不渲染动态内容
  if (!mounted || !stats) {
    return (
      <div className="flex items-center space-x-2 text-sm text-gray-500">
        <Users className="h-4 w-4" />
        <span>載入中...</span>
      </div>
    )
  }

  return (
    <div className="flex items-center space-x-2 text-sm text-gray-500">
      <Users className="h-4 w-4" />
      <span>已有 {formatNumber(stats.totalStarted)} 人學習了這門課程</span>
    </div>
  )
} 