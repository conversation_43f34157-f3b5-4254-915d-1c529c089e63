'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { 
  Settings, 
  CheckCircle, 
  ArrowRight, 
  ArrowLeft, 
  Play, 
  Clock,
  Target,
  Trophy,
  Star,
  Users,
  Zap,
  Database,
  Workflow,
  Home
} from 'lucide-react'
import Link from 'next/link'
import { getCourseStats, incrementCourseStarted, incrementCourseCompleted, formatNumber } from '../../../lib/course-stats'
import { CourseStats } from '../components/course-stats'

// n8n課程專用導航組件
function N8nCourseNav() {
  return (
    <nav className="bg-white shadow-sm border-b sticky top-0 z-40">
      <div className="max-w-4xl mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/" className="flex items-center text-gray-600 hover:text-gray-900">
                <ArrowLeft className="h-4 w-4 mr-1" />
                返回首頁
              </Link>
            </Button>
            <div className="h-4 w-px bg-gray-300"></div>
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-green-600" />
              <span className="font-medium text-gray-900">n8n免費入門課程 - 互動模式</span>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/n8n-course" className="text-gray-600 hover:text-gray-900">
                靜態版本
              </Link>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link href="/n8n" className="flex items-center">
                <Home className="h-4 w-4 mr-1" />
                n8n專題首頁
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </nav>
  )
}

// n8n課程步驟數據
const courseSteps = [
  {
    id: 1,
    title: "n8n基礎介紹：什麼是自動化工作流？",
    type: "video",
    duration: "8分鐘",
    content: {
      description: "了解n8n是什麼，為什麼選擇n8n，以及它如何幫助你實現工作流自動化。",
      keyPoints: [
        "n8n的核心概念和特點",
        "開源vs商業化自動化工具對比",
        "n8n在實際工作中的應用場景"
      ]
    }
  },
  {
    id: 2,
    title: "n8n界面操作：掌握基本操作技巧",
    type: "interactive",
    duration: "10分鐘",
    content: {
      description: "通過實際演示學習n8n的界面操作，掌握創建和編輯工作流的基本技能。",
      principles: [
        {
          title: "節點(Node)概念",
          description: "每個節點代表一個操作或服務，通過連接節點來構建自動化流程",
          example: "HTTP請求節點 → 數據處理節點 → 發送郵件節點"
        },
        {
          title: "連接和數據傳遞",
          description: "理解節點之間的數據流動，學會配置輸入輸出參數",
          example: "前一個節點的輸出會自動成為下一個節點的輸入"
        },
        {
          title: "觸發器設置",
          description: "設置工作流的啟動條件，可以是定時、Webhook或手動觸發",
          example: "每天上午9點自動執行，或通過API調用觸發"
        }
      ]
    }
  },
  {
    id: 3,
    title: "實戰案例：構建第一個自動化工作流",
    type: "practice",
    duration: "15分鐘",
    content: {
      description: "通過三個實用的自動化案例，學會如何使用n8n解決實際工作問題。",
      cases: [
        {
          industry: "數據同步",
          company: "Google Sheets → Notion",
          highlights: ["自動數據導入", "格式轉換處理", "錯誤處理機制"]
        },
        {
          industry: "通知自動化",
          company: "郵件 → Slack 通知",
          highlights: ["郵件內容解析", "條件判斷邏輯", "多渠道通知"]
        },
        {
          industry: "API集成",
          company: "天氣API → 每日推送",
          highlights: ["外部API調用", "數據格式化", "定時任務設置"]
        }
      ]
    }
  },
  {
    id: 4,
    title: "進階技巧：複雜工作流設計",
    type: "tools",
    duration: "8分鐘",
    content: {
      description: "學習n8n的高級功能，包括條件分支、循環處理、錯誤處理等進階技巧。",
      tools: [
        {
          name: "條件分支(IF節點)",
          type: "邏輯控制",
          price: "免費功能",
          features: ["多條件判斷", "複雜邏輯組合", "動態路由"],
          suitable: "需要智能決策的工作流"
        },
        {
          name: "循環處理(Loop節點)",
          type: "批量操作",
          price: "免費功能",
          features: ["批量數據處理", "迭代操作", "性能優化"],
          suitable: "大量數據處理場景"
        },
        {
          name: "錯誤處理(Error Trigger)",
          type: "穩定性保障",
          price: "免費功能",
          features: ["異常捕獲", "重試機制", "報警通知"],
          suitable: "生產環境的可靠性"
        }
      ]
    }
  },
  {
    id: 5,
    title: "部署與優化：讓你的自動化工作流穩定運行",
    type: "summary",
    duration: "6分鐘",
    content: {
      description: "學習如何部署n8n工作流到生產環境，以及性能優化和監控的最佳實踐。",
      actionPlan: [
        "選擇合適的部署方式（雲端vs自架）",
        "配置工作流的監控和日誌",
        "設置錯誤通知和備份策略",
        "優化工作流性能和資源消耗",
        "建立工作流文檔和維護流程"
      ],
      nextSteps: "完成這個免費課程後，你可以繼續探索n8n的更多高級功能，或者查看我們的其他自動化工具課程。"
    }
  }
]

export default function N8nCourseInteractivePage() {
  const [currentStep, setCurrentStep] = useState(1)
  const [completedSteps, setCompletedSteps] = useState<number[]>([])
  const [showUpgrade, setShowUpgrade] = useState(false)
  const [stats, setStats] = useState(getCourseStats())

  // 從本地存儲加載進度
  useEffect(() => {
    const savedProgress = localStorage.getItem('n8n-course-progress')
    if (savedProgress) {
      const { currentStep: saved, completed } = JSON.parse(savedProgress)
      setCurrentStep(saved)
      setCompletedSteps(completed)
    }
    
    // 增加開始課程統計
    incrementCourseStarted()
    setStats(getCourseStats())
  }, [])

  // 保存進度到本地存儲
  useEffect(() => {
    localStorage.setItem('n8n-course-progress', JSON.stringify({
      currentStep,
      completed: completedSteps
    }))
  }, [currentStep, completedSteps])

  const progress = (completedSteps.length / courseSteps.length) * 100

  const handleStepComplete = () => {
    if (!completedSteps.includes(currentStep)) {
      setCompletedSteps([...completedSteps, currentStep])
    }
    
    if (currentStep < courseSteps.length) {
      setCurrentStep(currentStep + 1)
    } else {
      // 完成課程統計
      incrementCourseCompleted()
      setStats(getCourseStats())
      setShowUpgrade(true)
    }
  }

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const currentStepData = courseSteps.find(step => step.id === currentStep)
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50">
      {/* 導航欄 */}
      <N8nCourseNav />
      
      {/* 課程頭部 */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Settings className="h-8 w-8 text-green-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  n8n自動化工具免費入門課程
                </h1>
                <p className="text-gray-600">免費學習 • 無需登錄 • 45分鐘掌握基礎</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Badge variant="secondary" className="text-green-700 bg-green-100">
                <Trophy className="w-4 h-4 mr-1" />
                {completedSteps.length}/{courseSteps.length} 已完成
              </Badge>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-gray-600">
              <span>課程進度</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        </div>
      </div>

      {/* 主要內容區域 */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* 左側：課程大綱 */}
          <div className="lg:col-span-1">
            <Card className="p-4 sticky top-4">
              <h3 className="font-semibold mb-4 text-gray-900">課程大綱</h3>
              <div className="space-y-2">
                {courseSteps.map((step) => (
                  <button
                    key={step.id}
                    onClick={() => setCurrentStep(step.id)}
                    className={`w-full text-left p-3 rounded-lg transition-colors ${
                      currentStep === step.id
                        ? 'bg-green-100 text-green-700 border border-green-200'
                        : 'hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      {completedSteps.includes(step.id) ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <div className={`w-5 h-5 rounded-full border-2 ${
                          currentStep === step.id ? 'border-green-500' : 'border-gray-300'
                        }`} />
                      )}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          第{step.id}課
                        </p>
                        <p className="text-xs text-gray-500 flex items-center">
                          <Clock className="w-3 h-3 mr-1" />
                          {step.duration}
                        </p>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </Card>
          </div>
          
          {/* 右側：課程內容 */}
          <div className="lg:col-span-3">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="p-8">
                  {currentStepData && (
                    <>
                      <div className="mb-6">
                        <Badge className="mb-3">
                          第 {currentStep} 課 · {currentStepData.duration}
                        </Badge>
                        <h2 className="text-2xl font-bold text-gray-900 mb-4">
                          {currentStepData.title}
                        </h2>
                        <p className="text-gray-600 leading-relaxed">
                          {currentStepData.content.description}
                        </p>
                      </div>
                    
                      {/* 根據不同類型渲染不同內容 */}
                      {currentStepData.type === 'video' && (
                        <div className="space-y-6">
                          <div className="bg-gray-100 rounded-lg p-8 text-center">
                            <Play className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                            <p className="text-gray-600">視頻播放器佔位</p>
                            <p className="text-sm text-gray-500 mt-2">
                              實際項目中這裡會是n8n操作演示視頻
                            </p>
                          </div>
                          <div>
                            <h4 className="font-semibold mb-3">本節要點：</h4>
                            <ul className="space-y-2">
                              {currentStepData.content.keyPoints?.map((point, index) => (
                                <li key={index} className="flex items-start">
                                  <Target className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                                  <span>{point}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    
                      {currentStepData.type === 'interactive' && (
                        <div className="space-y-6">
                          {currentStepData.content.principles?.map((principle, index) => (
                            <Card key={index} className="p-6 border-l-4 border-l-green-500">
                              <h4 className="font-bold text-lg mb-2">{principle.title}</h4>
                              <p className="text-gray-600 mb-3">{principle.description}</p>
                              <div className="bg-green-50 p-4 rounded-lg">
                                <p className="text-sm font-medium text-green-800">示例：</p>
                                <p className="text-green-700">{principle.example}</p>
                              </div>
                            </Card>
                          ))}
                        </div>
                      )}
                    
                      {currentStepData.type === 'practice' && (
                        <div className="space-y-6">
                          {currentStepData.content.cases?.map((caseItem, index) => (
                            <Card key={index} className="p-6">
                              <div className="flex items-center mb-4">
                                <Badge variant="outline" className="mr-3">
                                  {caseItem.industry}
                                </Badge>
                                <h4 className="font-bold text-lg">{caseItem.company}</h4>
                              </div>
                              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                {caseItem.highlights.map((highlight, hIndex) => (
                                  <div key={hIndex} className="bg-green-50 p-3 rounded-lg">
                                    <Workflow className="h-4 w-4 text-green-600 mb-2" />
                                    <p className="text-sm font-medium text-green-800">
                                      {highlight}
                                    </p>
                                  </div>
                                ))}
                              </div>
                            </Card>
                          ))}
                        </div>
                      )}
                    
                      {currentStepData.type === 'tools' && (
                        <div className="space-y-4">
                          {currentStepData.content.tools?.map((tool, index) => (
                            <Card key={index} className="p-6">
                              <div className="flex justify-between items-start mb-4">
                                <div>
                                  <h4 className="font-bold text-lg">{tool.name}</h4>
                                  <Badge variant="secondary" className="mt-1">
                                    {tool.type}
                                  </Badge>
                                </div>
                                <div className="text-right">
                                  <p className="font-semibold text-green-600">{tool.price}</p>
                                </div>
                              </div>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                  <p className="font-medium mb-2">主要功能：</p>
                                  <ul className="text-sm space-y-1">
                                    {tool.features.map((feature, fIndex) => (
                                      <li key={fIndex} className="flex items-center">
                                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                                        {feature}
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                                <div>
                                  <p className="font-medium mb-2">適用場景：</p>
                                  <p className="text-sm text-gray-600">{tool.suitable}</p>
                                </div>
                              </div>
                            </Card>
                          ))}
                        </div>
                      )}
                    
                      {currentStepData.type === 'summary' && (
                        <div className="space-y-6">
                          <Card className="p-6 bg-gradient-to-r from-green-50 to-blue-50">
                            <h4 className="font-bold text-lg mb-4 text-green-900">
                              你的n8n學習行動計劃
                            </h4>
                            <ol className="space-y-3">
                              {currentStepData.content.actionPlan?.map((step, index) => (
                                <li key={index} className="flex items-start">
                                  <span className="flex-shrink-0 w-6 h-6 bg-green-600 text-white text-sm font-bold rounded-full flex items-center justify-center mr-3 mt-0.5">
                                    {index + 1}
                                  </span>
                                  <span className="text-green-800">{step}</span>
                                </li>
                              ))}
                            </ol>
                          </Card>
                          <Card className="p-6 border-l-4 border-l-green-500">
                            <h4 className="font-bold text-lg mb-3 text-green-800">
                              繼續探索n8n
                            </h4>
                            <p className="text-gray-700 mb-4">
                              {currentStepData.content.nextSteps}
                            </p>
                            <div className="flex flex-col sm:flex-row gap-3">
                              <Button asChild className="bg-green-600 hover:bg-green-700">
                                <Link href="/n8n">
                                  n8n專題首頁
                                  <ArrowRight className="ml-2 h-4 w-4" />
                                </Link>
                              </Button>
                              <Button variant="outline" asChild>
                                <Link href="/all-courses">
                                  瀏覽更多課程
                                  <ArrowRight className="ml-2 h-4 w-4" />
                                </Link>
                              </Button>
                            </div>
                          </Card>
                        </div>
                      )}

                      {/* 課程導航 */}
                      <div className="flex justify-between items-center mt-8 pt-6 border-t">
                        <Button
                          variant="outline"
                          onClick={handlePrevStep}
                          disabled={currentStep === 1}
                          className="flex items-center"
                        >
                          <ArrowLeft className="mr-2 h-4 w-4" />
                          上一課
                        </Button>

                        <CourseStats />

                        <Button
                          onClick={handleStepComplete}
                          className="flex items-center bg-green-600 hover:bg-green-700"
                        >
                          {currentStep === courseSteps.length ? '完成課程' : '下一課'}
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </>
                  )}
                </Card>
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>
      
      {/* 升級彈窗 */}
      <AnimatePresence>
        {showUpgrade && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowUpgrade(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-lg p-8 max-w-md w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center">
                <Trophy className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
                <h3 className="text-xl font-bold mb-2">恭喜完成n8n入門課程！</h3>
                <p className="text-gray-600 mb-6">
                  你已經掌握了n8n自動化工具的基礎知識。現在可以開始實踐自己的自動化項目了！
                </p>
                <div className="space-y-3">
                  <Button asChild className="w-full bg-green-600 hover:bg-green-700">
                    <Link href="/n8n">
                      返回n8n專題
                    </Link>
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => setShowUpgrade(false)}
                  >
                    稍後再說
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
} 