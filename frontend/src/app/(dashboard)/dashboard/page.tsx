'use client'

import { useEffect, useState } from 'react'
import { Card } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { StatsService, DashboardStats, RecentLearning } from '@/lib/services/stats.service'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import Link from 'next/link'

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [recentLearning, setRecentLearning] = useState<RecentLearning[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [dashboardStats, recentLearningData] = await Promise.all([
          StatsService.getDashboardStats(),
          StatsService.getRecentLearning(),
        ])
        setStats(dashboardStats)
        setRecentLearning(recentLearningData)
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">我的學習</h1>
      
      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-2">已學課程</h3>
          <p className="text-3xl font-bold">{stats?.studiedCourses || 0}</p>
        </Card>
        
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-2">總體進度</h3>
          <div className="space-y-2">
            <Progress value={stats?.totalProgress || 0} />
            <p className="text-sm text-gray-500">{stats?.totalProgress || 0}%</p>
          </div>
        </Card>
        
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-2">已購課程</h3>
          <p className="text-3xl font-bold">{stats?.totalCourses || 0}</p>
        </Card>
        
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-2">獲得成就</h3>
          <p className="text-3xl font-bold">--</p>
          <p className="text-sm text-gray-500">即將推出</p>
        </Card>
      </div>

      {/* 最近學習 */}
      <div className="space-y-6">
        <h2 className="text-xl font-semibold">最近學習</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {recentLearning.map((course) => (
            <Link key={course.courseId} href={`/my-courses/${course.courseId}`}>
              <Card className="p-6 hover:bg-gray-50 transition-colors">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medium mb-1">{course.courseName}</h3>
                    <p className="text-sm text-gray-500">
                      上次學習：{formatDistanceToNow(new Date(course.lastVisitTime), { 
                        addSuffix: true,
                        locale: zhCN,
                      })}
                    </p>
                  </div>
                  <div>
                    <div className="flex justify-between text-sm text-gray-500 mb-2">
                      <span>學習進度</span>
                      <span>{Math.min(Math.max(course.progress || 0, 0), 100)}%</span>
                    </div>
                    <Progress value={Math.min(Math.max(course.progress || 0, 0), 100)} />
                  </div>
                  {course.lastLesson && (
                    <p className="text-sm text-gray-500">
                      最近學習：第 {course.lastLesson.order} 章 - {course.lastLesson.title}
                    </p>
                  )}
                </div>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </div>
  )
} 