'use client';

import { useEffect, useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { api } from '@/lib/api';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

interface Order {
  id: string;
  orderNo: string;
  amount: number;
  status: string;
  createdAt: string;
  course?: {
    id: string;
    title: string;
  };
}

export default function OrdersPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        const response = await api.get('/api/payment/orders');
        setOrders(response.data);
      } catch (error) {
        toast({
          variant: 'destructive',
          title: '获取订单失败',
          description: '请稍后重试',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, [toast]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Badge variant="secondary">待支付</Badge>;
      case 'PAID':
        return <Badge variant="default" className="bg-green-500">已支付</Badge>;
      case 'EXPIRED':
        return <Badge variant="destructive">已过期</Badge>;
      case 'CANCELLED':
        return <Badge variant="outline">已取消</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">我的订单</h1>
      <div className="space-y-4">
        {orders.length === 0 ? (
          <div className="text-center text-muted-foreground py-8">
            暂无订单记录
          </div>
        ) : (
          orders.map((order) => (
            <Card key={order.id} className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <span className="font-medium">订单号：{order.orderNo}</span>
                    {getStatusBadge(order.status)}
                  </div>
                  <p className="text-muted-foreground">
                    课程：{order.course?.title || '未知课程'}
                  </p>
                  <p className="text-muted-foreground">
                    金额：${Number(order.amount).toFixed(2)}
                  </p>
                </div>
                <div className="flex flex-col items-end gap-2">
                  <div className="text-sm text-muted-foreground">
                    创建时间：{format(new Date(order.createdAt), 'yyyy年MM月dd日 HH:mm', { locale: zhCN })}
                  </div>
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`/orders/${order.orderNo}`}>
                      查看详情
                    </Link>
                  </Button>
                </div>
              </div>
            </Card>
          ))
        )}
      </div>
    </div>
  );
} 