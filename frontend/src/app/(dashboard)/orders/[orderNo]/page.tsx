'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { api } from '@/lib/api';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

interface Order {
  orderNo: string;
  status: string;
  amount: number;
  createdAt: string;
  course?: {
    id: string;
    title: string;
    price: number;
  };
  payment?: {
    paymentNo: string;
    status: string;
    paidAt?: string;
  };
  refund?: {
    refundNo: string;
    status: string;
    amount: number;
    reason?: string;
    createdAt: string;
  };
}

export default function OrderDetailPage() {
  const { orderNo } = useParams();
  const { toast } = useToast();
  const [order, setOrder] = useState<Order | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchOrder = async () => {
      try {
        const response = await api.get(`/api/payment/orders/${orderNo}`);
        setOrder(response.data);
      } catch (error) {
        toast({
          title: '获取订单详情失败',
          description: '请稍后重试',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrder();
  }, [orderNo, toast]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Badge variant="secondary">待支付</Badge>;
      case 'PAID':
        return <Badge variant="default" className="bg-green-500">已支付</Badge>;
      case 'EXPIRED':
        return <Badge variant="destructive">已过期</Badge>;
      case 'CANCELLED':
        return <Badge variant="outline">已取消</Badge>;
      case 'REFUNDED':
        return <Badge variant="destructive">已退款</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
      </div>
    );
  }

  if (!order) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[200px] gap-4">
        <p className="text-gray-500">订单不存在</p>
        <Button variant="outline" asChild>
          <Link href="/orders">
            返回订单列表
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center justify-between mb-6">
        <Button variant="ghost" asChild>
          <Link href="/orders">
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回订单列表
          </Link>
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>订单信息</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-500">订单号</span>
                <span>{order.orderNo}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">状态</span>
                {getStatusBadge(order.status)}
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">金额</span>
                <span>¥{order.amount}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">创建时间</span>
                <span>{format(new Date(order.createdAt), 'yyyy年MM月dd日 HH:mm:ss', { locale: zhCN })}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {order.course && (
          <Card>
            <CardHeader>
              <CardTitle>课程信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-500">课程名称</span>
                  <span>{order.course.title}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">课程价格</span>
                  <span>¥{order.course.price}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {order.payment && (
          <Card>
            <CardHeader>
              <CardTitle>支付信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-500">支付单号</span>
                  <span>{order.payment.paymentNo}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">支付状态</span>
                  {getStatusBadge(order.payment.status)}
                </div>
                {order.payment.paidAt && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">支付时间</span>
                    <span>{format(new Date(order.payment.paidAt), 'yyyy年MM月dd日 HH:mm:ss', { locale: zhCN })}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {order.refund && (
          <Card>
            <CardHeader>
              <CardTitle>退款信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-500">退款单号</span>
                  <span>{order.refund.refundNo}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">退款状态</span>
                  {getStatusBadge(order.refund.status)}
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">退款金额</span>
                  <span>¥{order.refund.amount}</span>
                </div>
                {order.refund.reason && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">退款原因</span>
                    <span>{order.refund.reason}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-gray-500">退款时间</span>
                  <span>{format(new Date(order.refund.createdAt), 'yyyy年MM月dd日 HH:mm:ss', { locale: zhCN })}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
} 