'use client'

import { useState } from 'react'
import { useAuth } from '@/auth/contexts/simplified-auth-context'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useToast } from '@/hooks/use-toast'
import { UserService } from '@/lib/services/user.service'

export default function ProfilePage() {
  const { user } = useAuth()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  })

  const validateForm = () => {
    if (formData.name.length < 2) {
      toast({
        title: '驗證失敗',
        description: '用戶名稱至少需要2個字',
        variant: 'destructive',
      })
      return false
    }

    if (formData.newPassword && formData.newPassword !== formData.confirmPassword) {
      toast({
        title: '驗證失敗',
        description: '兩次輸入的密碼不一致',
        variant: 'destructive',
      })
      return false
    }

    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!validateForm()) return
    setIsLoading(true)

    try {
      // 更新用戶名
      if (formData.name !== user?.name) {
        await UserService.updateUser(user!.id, { name: formData.name })
      }

      // 如果輸入了密碼，則更新密碼
      if (formData.newPassword) {
        await UserService.updatePassword({
          oldPassword: formData.currentPassword,
          newPassword: formData.newPassword,
        })
      }

      toast({
        title: '更新成功',
        description: '個人資料已更新',
      })

      // 清空密碼字段
      setFormData({
        ...formData,
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      })
    } catch (error) {
      toast({
        title: '更新失敗',
        description: error instanceof Error ? error.message : '更新個人資料時發生錯誤',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">個人資料</h1>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold">基本資訊</h2>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">用戶名稱</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) =>
                    setFormData({ ...formData, name: e.target.value })
                  }
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">電郵</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  disabled
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="currentPassword">當前密碼</Label>
                <Input
                  id="currentPassword"
                  type="password"
                  value={formData.currentPassword}
                  onChange={(e) =>
                    setFormData({ ...formData, currentPassword: e.target.value })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="newPassword">新密碼</Label>
                <Input
                  id="newPassword"
                  type="password"
                  value={formData.newPassword}
                  onChange={(e) =>
                    setFormData({ ...formData, newPassword: e.target.value })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">確認新密碼</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  value={formData.confirmPassword}
                  onChange={(e) =>
                    setFormData({ ...formData, confirmPassword: e.target.value })
                  }
                />
              </div>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? '儲存中...' : '儲存'}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 