'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Course, CourseLesson, CourseService } from '@/lib/services/course.service';
import { Clock, BookOpen, CheckCircle } from 'lucide-react';
import Link from 'next/link';

export default function CoursePage() {
  const params = useParams();
  const courseId = params.id as string;

  const [course, setCourse] = useState<Course | null>(null);
  const [lessons, setLessons] = useState<CourseLesson[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCourseData = async () => {
      try {
        const [courseData, lessonsData] = await Promise.all([
          CourseService.getCourse(courseId),
          CourseService.getCourseLessons(courseId),
        ]);
        
        setCourse(courseData);
        setLessons(lessonsData);
      } catch (err) {
        setError('加载课程信息失败，请稍后重试');
        console.error('Failed to fetch course:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchCourseData();
  }, [courseId]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <LoadingSpinner />
      </div>
    );
  }

  if (error || !course) {
    return (
      <div className="text-center text-red-500 min-h-[400px] flex items-center justify-center">
        {error || '课程不存在'}
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {/* 课程头部信息 */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Badge variant="secondary">{course.level}</Badge>
            <Badge variant="default" className="bg-blue-500">
              已购买
            </Badge>
          </div>
          <h1 className="text-3xl font-bold mb-4">{course.title}</h1>
          <p className="text-lg text-muted-foreground mb-6">{course.description}</p>
          <div className="flex items-center gap-6 text-muted-foreground">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              <span>{course.duration}</span>
            </div>
            <div className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              <span>{course.lessonsCount} 课时</span>
            </div>
          </div>
        </div>

        {/* 课程章节列表 */}
        <div className="space-y-4">
          <h2 className="text-2xl font-semibold mb-4">课程大纲</h2>
          {lessons.map((lesson) => (
            <Card key={lesson.id} className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className="text-muted-foreground">第 {lesson.order} 章</span>
                    {lesson.isCompleted && (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    )}
                  </div>
                  <h3 className="text-lg font-medium mt-1">{lesson.title}</h3>
                  <p className="text-muted-foreground mt-1">{lesson.description}</p>
                  <div className="flex items-center gap-2 mt-2 text-sm text-muted-foreground">
                    <Clock className="h-4 w-4" />
                    <span>{lesson.duration}</span>
                  </div>
                </div>
                <Button asChild variant="outline">
                  <Link href={`/my-courses/${course.id}/lessons/${lesson.id}${lesson.isCompleted ? '?mode=review' : ''}`}>
                    {lesson.isCompleted ? (
                      <>
                        <CheckCircle className="mr-2 h-4 w-4" />
                        复习
                      </>
                    ) : (
                      '开始学习'
                    )}
                  </Link>
                </Button>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
} 