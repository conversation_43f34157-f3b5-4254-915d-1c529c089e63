'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Course, CourseLesson, CourseService } from '@/lib/services/course.service';
import { ChevronLeft } from 'lucide-react';
import Link from 'next/link';
import { useToast } from '@/components/ui/use-toast';
import { InteractiveLesson } from '@/components/course/interactive-lesson';
import { parseMarkdownToSteps } from '@/lib/utils/markdown';

export default function LessonPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const courseId = params.id as string;
  const lessonId = params.lessonId as string;

  const [course, setCourse] = useState<Course | null>(null);
  const [lessons, setLessons] = useState<CourseLesson[]>([]);
  const [lesson, setLesson] = useState<CourseLesson | null>(null);
  const [steps, setSteps] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [courseData, lessonsData] = await Promise.all([
          CourseService.getCourse(courseId),
          CourseService.getCourseLessons(courseId),
        ]);

        setCourse(courseData);
        setLessons(lessonsData);
        const currentLesson = lessonsData.find((l) => l.id === lessonId);
        if (currentLesson) {
          setLesson(currentLesson);
          // 加载课程内容
          try {
            const content = await CourseService.getLessonContent(courseId, lessonId);
            // 解析课程内容为步骤
            const parsedSteps = parseMarkdownToSteps(content);
            setSteps(parsedSteps);
          } catch (err) {
            console.error('Failed to load lesson content:', err);
            setError('加载课程内容失败，请刷新页面重试');
          }
        }
      } catch (err) {
        setError('加载课程信息失败，请稍后重试');
        console.error('Failed to fetch lesson:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [courseId, lessonId]);

  const handleComplete = async () => {
    if (!lesson) {
      console.error('No lesson found');
      return;
    }

    try {
      console.log('开始处理课程完成事件', {
        courseId,
        lessonId,
        lessonOrder: lesson.order,
        lessonsCount: lessons.length
      });

      // 获取下一章节
      const currentIndex = lessons.findIndex((l) => l.id === lessonId);
      console.log('当前章节索引:', currentIndex);
      
      const nextLesson = lessons[currentIndex + 1];
      console.log('下一章节:', nextLesson);
      
      // 更新当前课程进度
      console.log('正在更新课程进度...');
      await CourseService.updateProgress(courseId, lessonId);
      console.log('课程进度更新完成');
      
      if (nextLesson) {
        // 如果有下一节课，更新完进度后直接跳转
        const nextUrl = `/my-courses/${courseId}/lessons/${nextLesson.id}`;
        console.log('准备跳转到下一节:', nextUrl);
        window.location.href = nextUrl;
      } else {
        // 如果是最后一节课，显示完成提示后返回课程页面
        console.log('这是最后一节课，准备返回课程页面');
        toast({
          title: '课程完成',
          description: '恭喜你完成了本章节的学习！',
        });
        setTimeout(() => {
          console.log('返回课程页面');
          window.location.href = `/my-courses/${courseId}`;
        }, 1500);
      }
    } catch (err) {
      console.error('课程完成处理失败:', err);
      toast({
        variant: 'destructive',
        title: '更新失败',
        description: '请稍后重试',
      });
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <LoadingSpinner />
      </div>
    );
  }

  if (error || !course || !lesson) {
    return (
      <div className="text-center text-red-500 min-h-[400px] flex items-center justify-center">
        {error || '课程不存在'}
      </div>
    );
  }

  return (
    <div className="container mx-auto">
      <div className="mb-6">
        <Button variant="ghost" asChild>
          <Link href={`/my-courses/${courseId}`}>
            <ChevronLeft className="mr-2 h-4 w-4" />
            返回课程
          </Link>
        </Button>
      </div>

      <InteractiveLesson
        title={lesson.title}
        steps={steps}
        onComplete={handleComplete}
        hasNextLesson={lessons.findIndex((l) => l.id === lessonId) < lessons.length - 1}
      />
    </div>
  );
} 