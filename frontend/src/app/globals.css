@tailwind base;
@tailwind components;
@tailwind utilities;
 
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
 
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
 
    --primary: 217 91% 40%;
    --primary-foreground: 0 0% 100%;
 
    --secondary: 220 14% 96%;
    --secondary-foreground: 217 33% 17%;
 
    --muted: 220 14% 96%;
    --muted-foreground: 215 16% 47%;
 
    --accent: 24 94% 53%;
    --accent-foreground: 0 0% 100%;
 
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 217 91% 40%;
 
    --radius: 0.5rem;
  }
 
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
 
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
 
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
 
    --primary: 217 91% 40%;
    --primary-foreground: 0 0% 100%;
 
    --secondary: 217 33% 17%;
    --secondary-foreground: 210 40% 98%;
 
    --muted: 217 33% 17%;
    --muted-foreground: 215 20% 65%;
 
    --accent: 24 94% 53%;
    --accent-foreground: 0 0% 100%;
 
    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;
 
    --border: 217 33% 17%;
    --input: 217 33% 17%;
    --ring: 224 76% 48%;
  }
}
 
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
} 

/* Tour 样式 */
[data-tour-elem="popover"] {
  background-color: white !important;
  border-radius: 8px !important;
  padding: 1rem !important;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1) !important;
}

[data-tour-elem="mask"] {
  background-color: rgba(0, 0, 0, 0.5) !important;
}

[data-tour-elem="controls"] {
  margin-top: 1rem !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
}

[data-tour-elem="controls"] button {
  background-color: #4f46e5 !important;
  color: white !important;
  padding: 0.5rem 1rem !important;
  border-radius: 0.375rem !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  transition: opacity 0.2s ease !important;
}

[data-tour-elem="controls"] button:hover {
  opacity: 0.9 !important;
}

[data-tour-elem="controls"] button[disabled] {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
}

[data-tour-elem="dot"] {
  width: 8px !important;
  height: 8px !important;
  margin: 0 4px !important;
  background-color: #d1d5db !important;
  border-radius: 50% !important;
  transition: background-color 0.2s ease !important;
}

[data-tour-elem="dot"][data-tour-state="active"] {
  background-color: #4f46e5 !important;
}

[data-tour-elem="arrow"] {
  color: white !important;
}

[data-tour-elem="badge"] {
  background-color: #4f46e5 !important;
  color: white !important;
  padding: 4px 8px !important;
  border-radius: 4px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
}

/* Markdown Preview Styles */
.markdown-preview {
  @apply prose prose-sm max-w-none dark:prose-invert;
}

.markdown-preview pre {
  @apply bg-slate-900 text-slate-50 p-4 rounded-lg overflow-x-auto;
}

.markdown-preview code {
  @apply bg-slate-100 dark:bg-slate-800 px-1 py-0.5 rounded;
}

.markdown-preview pre code {
  @apply bg-transparent p-0;
}

.markdown-preview blockquote {
  @apply border-l-4 border-slate-300 dark:border-slate-700 pl-4 italic;
}

.markdown-preview table {
  @apply border-collapse border border-slate-300 dark:border-slate-700;
}

.markdown-preview th,
.markdown-preview td {
  @apply border border-slate-300 dark:border-slate-700 px-4 py-2;
}

.markdown-preview img {
  @apply max-w-full h-auto rounded-lg;
  transition: opacity 0.3s ease-in-out;
}

.markdown-preview .image-loading-container {
  @apply relative inline-block w-full;
  min-height: 200px;
  background-color: #f3f4f6;
  border-radius: 0.5rem;
}

.markdown-preview .image-loading-spinner {
  @apply absolute inset-0 flex items-center justify-center;
  color: #6366f1;
}

.markdown-preview .image-loading {
  @apply opacity-30;
}

.markdown-preview hr {
  @apply border-slate-300 dark:border-slate-700;
}

.markdown-preview a {
  @apply text-blue-600 dark:text-blue-400 hover:underline;
}

.markdown-preview ul,
.markdown-preview ol {
  @apply pl-6;
}

.markdown-preview ul {
  @apply list-disc;
}

.markdown-preview ol {
  @apply list-decimal;
}

/* KaTeX Math Styles */
.markdown-preview .katex-display {
  @apply overflow-x-auto overflow-y-hidden;
}

.markdown-preview .katex {
  @apply text-base leading-normal;
  font-size: 1.2em !important;
  line-height: 1.6 !important;
  font-family: "KaTeX_Main", "Times New Roman", serif !important;
}

.markdown-preview .katex-display > .katex {
  @apply text-lg leading-relaxed;
  font-size: 1.3em !important;
  text-align: center !important;
  padding: 0.5em 0 !important;
}

.markdown-preview .katex-html {
  @apply overflow-x-hidden overflow-y-visible;
}

.markdown-preview .katex .base {
  @apply my-1;
}

.markdown-preview .katex .mfrac .frac-line {
  @apply border-black dark:border-white;
  border-width: 0.06em !important;
}

.markdown-preview .katex .mfrac {
  margin: 0 0.25em !important;
}

.markdown-preview .katex .mfrac .frac-line {
  min-height: 0.06em !important;
  margin: 0.15em 0 !important;
}

.markdown-preview .katex .accent .accent-body {
  @apply w-auto;
}

.markdown-preview .katex .msupsub {
  @apply text-[0.85em];
}

.markdown-preview .katex .mathdefault {
  @apply italic;
}

.markdown-preview .katex-error {
  @apply text-red-500 dark:text-red-400;
}

/* 优化中文在数学公式中的显示 */
.markdown-preview .katex .mord.text {
  font-family: "KaTeX_Main", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif !important;
}

/* 优化分子分母的大小和间距 */
.markdown-preview .katex .mfrac .mfracnum,
.markdown-preview .katex .mfrac .mfracden {
  font-size: 0.95em !important;
  padding: 0.15em 0.3em !important;
}

/* 优化括号的大小和间距 */
.markdown-preview .katex .delimsizing.size1 {
  font-size: 1.4em !important;
}

.markdown-preview .katex .delimsizing.size2 {
  font-size: 1.6em !important;
}

.markdown-preview .katex .delimsizing.size3 {
  font-size: 1.8em !important;
}

.markdown-preview .katex .delimsizing.size4 {
  font-size: 2em !important;
}

/* 优化行内公式的显示 */
.markdown-preview .katex-inline {
  padding: 0.1em 0.2em !important;
  margin: 0 0.1em !important;
  font-size: 1.1em !important;
  background-color: rgba(248, 249, 250, 0.4) !important;
  border-radius: 0.25rem !important;
}

/* 深色模式适配 */
.dark .markdown-preview .katex-display {
  background-color: rgba(30, 41, 59, 0.7) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.dark .markdown-preview .katex-inline {
  background-color: rgba(30, 41, 59, 0.4) !important;
}

/* 优化数学符号的显示 */
.markdown-preview .katex .mathit {
  font-family: "KaTeX_Math", "Times New Roman", serif !important;
}

.markdown-preview .katex .mathrm {
  font-family: "KaTeX_Main", "Times New Roman", serif !important;
}

/* 优化根号的显示 */
.markdown-preview .katex .sqrt {
  padding-top: 0.2em !important;
}

.markdown-preview .katex .sqrt > .sqrt-sign {
  margin-right: 0.1em !important;
}

/* 优化上下标的位置 */
.markdown-preview .katex .msupsub > .vlist-t > .vlist-r > .vlist {
  text-align: left !important;
}

/* Mermaid Diagram Styles */
.markdown-preview .mermaid-diagram {
  @apply my-8 p-4 bg-white rounded-lg shadow-sm border border-slate-200;
  max-width: 100%;
  overflow-x: auto;
}

.markdown-preview .mermaid-diagram svg {
  @apply mx-auto;
  max-width: 100%;
  height: auto;
}

/* Markdown Preview Styles */
.markdown-preview {
  @apply prose prose-sm max-w-none dark:prose-invert;
}

.markdown-preview pre {
  @apply bg-slate-900 text-slate-50 p-4 rounded-lg overflow-x-auto;
}

.markdown-preview code {
  @apply bg-slate-100 dark:bg-slate-800 px-1 py-0.5 rounded;
}

.markdown-preview pre code {
  @apply bg-transparent p-0;
}

@media print {
  /* 隐藏不需要打印的元素 */
  .print\\:hidden {
    display: none !important;
  }

  /* 优化打印布局 */
  .print\\:m-0 {
    margin: 0 !important;
  }

  .print\\:p-0 {
    padding: 0 !important;
  }

  /* 确保图片显示完整 */
  img {
    max-width: 100%;
    page-break-inside: avoid;
  }

  /* 优化分页 */
  h1, h2, h3 {
    page-break-after: avoid;
  }

  /* 移除对话框样式 */
  [role="dialog"] {
    position: static !important;
    transform: none !important;
    max-height: none !important;
    height: auto !important;
    background: none !important;
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    box-shadow: none !important;
  }

  /* 优化内容布局 */
  .prose {
    max-width: none !important;
  }

  /* 设置合适的字体大小 */
  body {
    font-size: 12pt;
  }

  h1 {
    font-size: 24pt;
  }

  h2 {
    font-size: 18pt;
  }

  h3 {
    font-size: 14pt;
  }
}

/* AI 生成插画样式 */
.generated-image-container {
  @apply relative rounded-lg border-2 border-slate-200 overflow-hidden aspect-square;
  background-color: #f8fafc;
}

.generated-image-loading {
  @apply absolute inset-0 bg-slate-50 flex items-center justify-center;
}

.generated-image {
  @apply w-full h-full object-cover relative z-10 transition-opacity duration-300;
  opacity: 0;
}

.generated-image.loaded {
  opacity: 1;
}

.generated-image-actions {
  @apply absolute bottom-0 left-0 right-0 p-2 bg-black/50 z-20;
}

/* 隐藏滚动条但保持可滚动 */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;     /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

@layer utilities {
  /* 添加渐入动画 */
  .animate-fadeIn {
    animation: fadeIn 0.5s ease-in-out forwards;
  }

  /* 添加滑入动画 */
  .animate-slideIn {
    animation: slideIn 0.5s ease-out forwards;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 新增的自定义样式 */
@layer components {
  .card {
    @apply rounded-lg border bg-card p-6 shadow-sm transition-shadow hover:shadow-md;
  }

  .section {
    @apply py-12 md:py-16 lg:py-20;
  }

  .container {
    @apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
  }

  .heading-1 {
    @apply text-4xl font-bold tracking-tight text-gray-900 dark:text-gray-50 sm:text-5xl md:text-6xl;
  }

  .heading-2 {
    @apply text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-50 sm:text-4xl;
  }

  .heading-3 {
    @apply text-2xl font-bold tracking-tight text-gray-900 dark:text-gray-50 sm:text-3xl;
  }

  .prose-content {
    @apply prose prose-lg prose-blue mx-auto dark:prose-invert;
  }

  .nav-link {
    @apply rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-200 dark:hover:bg-gray-800 dark:hover:text-white;
  }

  .nav-link-active {
    @apply bg-primary text-white hover:bg-primary/90 dark:bg-primary dark:text-white dark:hover:bg-primary/90;
  }

  .btn {
    @apply inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }

  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }

  .btn-accent {
    @apply bg-accent text-accent-foreground hover:bg-accent/90;
  }

  .input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .select {
    @apply flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .checkbox {
    @apply h-4 w-4 rounded border border-primary text-primary focus:ring-primary;
  }

  .radio {
    @apply h-4 w-4 border border-primary text-primary focus:ring-primary;
  }
}
 