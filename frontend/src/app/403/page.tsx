'use client'

import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'

export default function ForbiddenPage() {
  const router = useRouter()

  return (
    <div className="flex h-screen flex-col items-center justify-center">
      <div className="text-center">
        <h1 className="text-6xl font-bold text-gray-900 mb-4">403</h1>
        <h2 className="text-2xl font-semibold text-gray-700 mb-4">
          访问被拒绝
        </h2>
        <p className="text-gray-500 mb-8">
          抱歉，您没有权限访问此页面
        </p>
        <div className="space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            返回上一页
          </Button>
          <Button
            onClick={() => router.replace('/dashboard')}
          >
            返回仪表盘
          </Button>
        </div>
      </div>
    </div>
  )
} 