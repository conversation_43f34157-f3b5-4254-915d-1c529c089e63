import type { Metadata } from 'next'
import { LandingNav } from '@/components/home/<USER>'
import { Footer } from '@/components/home/<USER>'
import Link from 'next/link'

export const metadata: Metadata = {
  title: 'n8n教學課程 | 開源自動化專題 | 開源自動化工具實戰課程',
  description: '學習使用n8n開源自動化工具，無需編程即可串接200+服務，包含Google、Notion、Slack等，提升工作效率。課程包含完整實戰案例。',
  keywords: ['n8n', '自動化', '開源工具', '工作流程自動化', 'nocode'],
  openGraph: {
    images: [
      {
        url: '/og-n8n.jpg',
        width: 1200,
        height: 630,
      },
    ],
  },
}
import { ArrowRight, Zap, Settings, Database, HelpCircle } from 'lucide-react'
import N8nHeroSection from '@/components/n8n/N8nHeroSection'
import N8nScenarioSection from '@/components/n8n/N8nScenarioSection'
import N8nFeaturesSection from '@/components/n8n/N8nFeaturesSection'

function N8nFAQSection() {
  const faqs = [
    {
      question: 'n8n 跟 Zapier、Make 有什麼不同？',
      answer: 'n8n 完全開源、可自架，無用量限制，且支援自訂邏輯，彈性更高。',
    },
    {
      question: 'n8n 難學嗎？需要寫程式嗎？',
      answer: 'n8n 介面直覺，無需程式基礎，課程會手把手帶你上手。',
    },
    {
      question: 'n8n 有哪些應用案例？',
      answer: '如自動化行銷、數據同步、通知推送、AI 串接等，課程中會有完整實戰。',
    },
    {
      question: 'n8n 支援哪些服務？',
      answer: '支援超過 200+ 服務，包括 Google、Notion、Slack、Line、各種 API 等。',
    },
  ]
  return (
    <section className="py-20 bg-green-50">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-green-700">n8n 常見問題</h2>
          <p className="mt-4 text-xl text-gray-700">解答你對 n8n 的疑問</p>
        </div>
        <div className="mt-12 space-y-6">
          {faqs.map((faq, idx) => (
            <div key={idx} className="bg-white rounded-xl shadow p-6 text-left">
              <div className="flex items-center mb-2">
                <HelpCircle className="h-5 w-5 text-green-500 mr-2" />
                <span className="font-semibold text-green-700">{faq.question}</span>
              </div>
              <div className="text-gray-700 text-lg">{faq.answer}</div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default function N8nPage() {
  return (
    <div className="flex min-h-screen flex-col bg-white">
      <LandingNav />
      <main>
        <N8nHeroSection />
        <N8nScenarioSection />
        <N8nFeaturesSection />
        <N8nFAQSection />
        <section className="py-16 bg-gradient-to-r from-green-50 to-blue-50">
          <div className="max-w-4xl mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold text-green-700 mb-6">開始你的n8n學習之旅</h2>
            <p className="text-lg text-gray-700 mb-8">
              選擇適合你的學習方式，從零開始掌握n8n自動化工具
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white rounded-lg shadow-lg p-8">
                <div className="text-green-600 mb-4">
                  <Settings className="h-12 w-12 mx-auto" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">免費體驗課程</h3>
                <p className="text-gray-600 mb-4">
                  45分鐘快速入門，無需登錄即可體驗完整學習流程
                </p>
                <ul className="text-sm text-gray-600 mb-6 space-y-2">
                  <li>• n8n基礎概念介紹</li>
                  <li>• 界面操作演示</li>
                  <li>• 實戰案例分析</li>
                  <li>• 進階技巧預覽</li>
                </ul>
                <Link href="/n8n-course" className="inline-flex items-center px-6 py-3 bg-green-100 hover:bg-green-200 text-green-700 rounded-md text-lg font-medium transition-colors w-full justify-center">
                  免費體驗課程
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </div>
              <div className="bg-white rounded-lg shadow-lg p-8 border-2 border-green-200">
                <div className="text-green-600 mb-4">
                  <Zap className="h-12 w-12 mx-auto" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">完整實戰課程</h3>
                <p className="text-gray-600 mb-4">
                  深度學習n8n，包含企業級應用和高級集成技巧
                </p>
                <ul className="text-sm text-gray-600 mb-6 space-y-2">
                  <li>• 完整項目實戰</li>
                  <li>• 企業級部署方案</li>
                  <li>• 高級集成技巧</li>
                  <li>• 專屬學習社群</li>
                </ul>
                <Link href="/all-courses/n8n" className="inline-flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-md text-lg font-medium transition-colors w-full justify-center">
                  查看完整課程
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  )
} 