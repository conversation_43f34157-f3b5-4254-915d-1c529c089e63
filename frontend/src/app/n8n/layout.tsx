import { ReactNode } from 'react'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'n8n 自動化專題｜n8n 是什麼？如何學會 n8n？常見問題全解答',
  description: 'n8n 是什麼？n8n 如何自動化工作？本頁詳解 n8n 特色、應用場景、常見問題，並引導你參加 n8n 實戰課程，快速掌握自動化技能。',
  keywords: [
    'n8n',
    'n8n 教學',
    'n8n 自動化',
    'n8n 流程',
    'n8n 課程',
    'n8n 是什麼',
    '自動化工具'
  ],
  alternates: { canonical: '/n8n' },
  openGraph: {
    locale: 'zh_TW',
    title: 'n8n 自動化專題｜n8n 是什麼？如何學會 n8n？常見問題全解答',
    description: 'n8n 是什麼？n8n 如何自動化工作？本頁詳解 n8n 特色、應用場景、常見問題，並引導你參加 n8n 實戰課程，快速掌握自動化技能。',
  },
}

export default function N8nLayout({ children }: { children: ReactNode }) {
  return <section>{children}</section>
} 