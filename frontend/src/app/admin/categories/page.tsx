"use client";
import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { tokenManager } from "@/auth/core/simplified-token-manager";
import { api } from "@/lib/api";
import { Input } from "@/components/ui/input";
import { <PERSON>alog, DialogContent, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface Category {
  id: string;
  name: string;
  description?: string;
}

export default function AdminCategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [editCategory, setEditCategory] = useState<Category | null>(null);
  const [form, setForm] = useState({ name: "", description: "" });

  useEffect(() => {
    fetchCategories();
  }, []);

  async function fetchCategories() {
    setLoading(true);
    try {
      const { data } = await api.get("/api/categories");
      setCategories(data);
    } catch (error) {
      console.error("獲取分類失敗:", error);
      alert(error instanceof Error ? error.message : "未知錯誤");
    } finally {
      setLoading(false);
    }
  }

  function handleEdit(category: Category) {
    setEditCategory(category);
    setForm({ name: category.name, description: category.description || "" });
    setOpen(true);
  }

  function handleAdd() {
    setEditCategory(null);
    setForm({ name: "", description: "" });
    setOpen(true);
  }

  async function handleSubmit() {
    if (editCategory) {
      // update
      const token = tokenManager.getToken();
      if (!token) throw new Error("未登入");
      
      await api.patch(`/api/categories/${editCategory.id}`, form);
    } else {
      // create
      const token = tokenManager.getToken();
      if (!token) throw new Error("未登入");
      
      await api.post("/api/categories", form);
    }
    setOpen(false);
    fetchCategories();
  }

  async function handleDelete(id: string) {
    if (!window.confirm("確定要刪除這個分類嗎？")) return;
    const token = tokenManager.getToken();
    if (!token) throw new Error("未登入");
    
    await api.delete(`/api/categories/${id}`);
    fetchCategories();
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">分類管理</h1>
        <Button onClick={handleAdd}>新增分類</Button>
      </div>
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>名稱</TableHead>
              <TableHead>描述</TableHead>
              <TableHead className="w-[120px]">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={3} className="h-24 text-center">加載中...</TableCell>
              </TableRow>
            ) : categories.length === 0 ? (
              <TableRow>
                <TableCell colSpan={3} className="h-24 text-center text-slate-500">暫無數據</TableCell>
              </TableRow>
            ) : (
              categories.map((cat) => (
                <TableRow key={cat.id}>
                  <TableCell>{cat.name}</TableCell>
                  <TableCell>{cat.description}</TableCell>
                  <TableCell>
                    <Button size="sm" variant="outline" onClick={() => handleEdit(cat)} className="mr-2">編輯</Button>
                    <Button size="sm" variant="destructive" onClick={() => handleDelete(cat.id)}>刪除</Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent>
          <DialogTitle>{editCategory ? "編輯分類" : "新增分類"}</DialogTitle>
          <div className="space-y-4 mt-4">
            <Input
              placeholder="分類名稱"
              value={form.name}
              onChange={e => setForm(f => ({ ...f, name: e.target.value }))}
            />
            <Input
              placeholder="描述（可選）"
              value={form.description}
              onChange={e => setForm(f => ({ ...f, description: e.target.value }))}
            />
            <Button onClick={handleSubmit} className="w-full">保存</Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}