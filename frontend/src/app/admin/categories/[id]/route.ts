import { NextRequest, NextResponse } from 'next/server';

const API_BASE = process.env.API_BASE_URL || 'http://localhost:3000';

function getTokenFromCookie(req: NextRequest) {
  const cookie = req.cookies.get('token')?.value;
  return cookie ? `Bearer ${cookie}` : '';
}

export async function PATCH(req: NextRequest, { params }: { params: { id: string } }) {
  const token = getTokenFromCookie(req);
  const body = await req.json();
  const res = await fetch(`${API_BASE}/categories/${params.id}`, {
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json', 'Authorization': token },
    body: JSON.stringify(body),
  });
  if (!res.ok) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: res.status });
  }
  const data = await res.json();
  return NextResponse.json(data);
}

export async function DELETE(req: NextRequest, { params }: { params: { id: string } }) {
  const token = getTokenFromCookie(req);
  const res = await fetch(`${API_BASE}/categories/${params.id}`, {
    method: 'DELETE',
    headers: { 'Authorization': token },
  });
  if (!res.ok) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: res.status });
  }
  const data = await res.json();
  return NextResponse.json(data);
} 