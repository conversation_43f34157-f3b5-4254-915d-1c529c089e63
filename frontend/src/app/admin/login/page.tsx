'use client'

import { useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { useAuth } from '@/auth/contexts/simplified-auth-context'
import { useAuthNavigation } from '@/auth/hooks/use-auth-navigation'
import { LoginForm } from '@/components/auth/login-form'
import { useToast } from '@/components/ui/use-toast'

export default function AdminLoginPage() {
  const searchParams = useSearchParams()
  const { adminLogin, isAuthenticated } = useAuth()
  const { navigateAfterAuth } = useAuthNavigation()
  const { toast } = useToast()

  // 处理登录
  const handleLogin = async (data: { email: string; password: string }) => {
    try {
      await adminLogin(data.email, data.password)
      const redirectTo = searchParams.get('redirectTo') || '/admin/dashboard'
      navigateAfterAuth(redirectTo)
      toast({
        title: '登录成功',
        description: '欢迎回来！',
      })
    } catch (error) {
      console.error('AdminLoginPage: 登录失败:', error)
      toast({
        title: '登录失败',
        description: error instanceof Error ? error.message : '请稍后重试',
        variant: 'destructive',
      })
      throw error
    }
  }

  // 如果已经认证，直接重定向
  useEffect(() => {
    if (isAuthenticated) {
      const redirectTo = searchParams.get('redirectTo') || '/admin/dashboard'
      navigateAfterAuth(redirectTo)
    }
  }, [isAuthenticated, searchParams, navigateAfterAuth])

  return (
    <div className="container relative min-h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      <div className="relative hidden h-full flex-col bg-muted p-10 text-white lg:flex dark:border-r">
        <div className="absolute inset-0 bg-zinc-900" />
        <div className="relative z-20 flex items-center text-lg font-medium">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="mr-2 h-6 w-6"
          >
            <path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3" />
          </svg>
          跨境电商学院
        </div>
        <div className="relative z-20 mt-auto">
          <blockquote className="space-y-2">
            <p className="text-lg">
              "跨境电商学院管理后台，为您提供专业的系统管理功能。"
            </p>
            <footer className="text-sm">系统管理员</footer>
          </blockquote>
        </div>
      </div>
      <div className="lg:p-8">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          <div className="flex flex-col space-y-2 text-center">
            <h1 className="text-2xl font-semibold tracking-tight">
              管理员登录
            </h1>
            <p className="text-sm text-muted-foreground">
              请输入您的管理员账号和密码
            </p>
          </div>
          <LoginForm onSubmit={handleLogin} />
        </div>
      </div>
    </div>
  )
}