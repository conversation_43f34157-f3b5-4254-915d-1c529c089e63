'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { 
  Settings, 
  CheckCircle, 
  ArrowRight, 
  ArrowLeft, 
  Play, 
  Clock,
  Target,
  Trophy,
  Star,
  Users,
  Zap,
  Database,
  Workflow
} from 'lucide-react'
import Link from 'next/link'
import { CourseNav } from '@/components/interactive-course/course-nav'
import { getCourseStats, incrementCourseStarted, incrementCourseCompleted, formatNumber } from '../../lib/course-stats'

// n8n課程步驟數據
const courseSteps = [
  {
    id: 1,
    title: "n8n基礎介紹：什麼是自動化工作流？",
    type: "video",
    duration: "8分鐘",
    content: {
      videoUrl: "/videos/n8n-intro.mp4", // 佔位視頻URL
      description: "了解n8n是什麼，為什麼選擇n8n，以及它如何幫助你實現工作流自動化。",
      keyPoints: [
        "n8n的核心概念和特點",
        "開源vs商業化自動化工具對比",
        "n8n在實際工作中的應用場景"
      ]
    }
  },
  {
    id: 2,
    title: "n8n安裝：快速部署你的自動化平台",
    type: "interactive",
    duration: "12分鐘",
    content: {
      description: "學習如何在不同環境中安裝和配置n8n，包括本地安裝、Docker部署和雲端部署方案。",
      principles: [
        {
          title: "本地安裝(npm方式)",
          description: "使用npm全局安裝n8n，適合快速測試和個人使用",
          example: "npm install n8n -g && n8n start"
        },
        {
          title: "Docker容器部署",
          description: "使用Docker快速部署，支持環境隔離和便捷管理",
          example: "docker run -it --rm --name n8n -p 5678:5678 n8nio/n8n"
        },
        {
          title: "雲端部署配置",
          description: "在雲服務器上部署n8n，配置域名、SSL證書和持久化存儲",
          example: "使用Nginx反向代理 + Let's Encrypt SSL + PostgreSQL數據庫"
        }
      ]
    }
  },
  {
    id: 3,
    title: "開啟使用：初次啟動n8n和界面導覽",
    type: "practice",
    duration: "8分鐘",
    content: {
      description: "學習如何首次啟動n8n，完成初始設置，並熟悉n8n的基本界面佈局。",
      cases: [
        {
          industry: "首次啟動",
          company: "n8n界面導覽",
          highlights: ["訪問n8n面板", "基本界面介紹", "帳戶設置與配置"],
          detailedSteps: [
            "打開瀏覽器，訪問 http://localhost:5678（默認端口）",
            "首次訪問會看到n8n的歡迎頁面，包含基本設置嚮導",
            "設置管理員帳戶：輸入電子郵件和密碼",
            "選擇個人使用或團隊使用模式",
            "完成初始設置後，進入n8n主界面"
          ],
          keyFeatures: [
            "左側菜單：工作流列表、模板庫、設置選項",
            "中間區域：工作流畫布，用於拖拽和連接節點",
            "右側面板：節點配置和數據預覽區域",
            "頂部工具欄：保存、執行、調試等功能按鈕"
          ]
        }
      ]
    }
  },
  {
    id: 4,
    title: "界面操作：掌握工作流編輯器使用技巧",
    type: "practice",
    duration: "10分鐘",
    content: {
      description: "深入學習n8n工作流編輯器的操作方法，掌握節點拖拽、連接和配置的核心技能。",
      cases: [
        {
          industry: "界面操作",
          company: "工作流編輯器使用",
          highlights: ["節點拖拽操作", "連線與配置", "測試與調試"],
          detailedSteps: [
            "點擊「創建新工作流」按鈕開始",
            "從左側節點庫中選擇觸發器節點（如Schedule Trigger）",
            "拖拽節點到畫布中央，雙擊進行配置",
            "設置觸發條件（如每5分鐘執行一次）",
            "添加執行節點，將兩個節點用線連接起來"
          ],
          keyFeatures: [
            "節點庫分類：觸發器、動作、條件、工具等",
            "拖拽操作：直接從節點庫拖到畫布即可添加",
            "連線方式：點擊節點的輸出點，拖拽到下一個節點的輸入點",
            "配置面板：雙擊節點打開配置，設置參數和選項"
          ]
        }
      ]
    }
  },
  {
    id: 5,
    title: "實戰演練：創建你的第一個自動化工作流",
    type: "practice",
    duration: "15分鐘",
    content: {
      description: "通過創建一個完整的定時任務工作流，體驗n8n自動化的強大功能。",
      cases: [
        {
          industry: "第一個工作流",
          company: "創建簡單定時任務",
          highlights: ["選擇觸發器", "添加執行節點", "保存並啟動工作流"],
          detailedSteps: [
            "選擇Schedule Trigger作為觸發器，設置每天上午9點執行",
            "添加HTTP Request節點，配置請求一個天氣API",
            "添加Edit Fields節點，提取需要的天氣數據",
            "添加Gmail節點，設置發送天氣報告郵件",
            "測試工作流：點擊「執行工作流」按鈕進行測試",
            "確認無誤後，點擊「激活」按鈕啟動定時任務"
          ],
          keyFeatures: [
            "工作流命名：給工作流起一個有意義的名稱",
            "節點測試：每個節點都可以單獨測試運行",
            "數據查看：查看每個節點的輸入和輸出數據",
            "錯誤處理：設置錯誤時的處理方式和通知"
          ]
        }
      ]
    }
  },
  {
    id: 6,
    title: "進階技巧：複雜工作流設計",
    type: "tools",
    duration: "12分鐘",
    content: {
      description: "學習n8n的高級功能，包括條件分支、循環處理、錯誤處理等進階技巧。",
      tools: [
        {
          name: "條件分支(IF節點)",
          type: "邏輯控制",
          price: "免費功能",
          features: ["多條件判斷", "複雜邏輯組合", "動態路由"],
          suitable: "需要智能決策的工作流"
        },
        {
          name: "循環處理(Loop節點)",
          type: "批量操作",
          price: "免費功能",
          features: ["批量數據處理", "迭代操作", "性能優化"],
          suitable: "大量數據處理場景"
        },
        {
          name: "錯誤處理(Error Trigger)",
          type: "穩定性保障",
          price: "免費功能",
          features: ["異常捕獲", "重試機制", "報警通知"],
          suitable: "生產環境的可靠性"
        }
      ]
    }
  },
  {
    id: 7,
    title: "部署與優化：讓你的自動化工作流穩定運行",
    type: "summary",
    duration: "10分鐘",
    content: {
      description: "學習如何部署n8n工作流到生產環境，以及性能優化和監控的最佳實踐。",
      actionPlan: [
        "選擇合適的部署方式（雲端vs自架）",
        "配置工作流的監控和日誌",
        "設置錯誤通知和備份策略",
        "優化工作流性能和資源消耗",
        "建立工作流文檔和維護流程"
      ],
      nextSteps: "完成這個免費課程後，你可以繼續學習我們的n8n高級課程，包括企業級部署、高級集成、自定義節點開發等更深入的內容。"
    }
  }
]

export default function InteractiveCourse() {
  const [currentStep, setCurrentStep] = useState(1)
  const [completedSteps, setCompletedSteps] = useState<number[]>([])
  const [showUpgrade, setShowUpgrade] = useState(false)
  const [stats, setStats] = useState({ totalStarted: 2847, totalCompleted: 1923, completionRate: 67.5 })
  const [isClient, setIsClient] = useState(false)

  // 確保只在客戶端執行
  useEffect(() => {
    setIsClient(true)
  }, [])

  // 從本地存儲加載進度
  useEffect(() => {
    if (!isClient) return
    
    const savedProgress = localStorage.getItem('n8n-course-progress')
    if (savedProgress) {
      try {
        const { currentStep: saved, completed } = JSON.parse(savedProgress)
        // 驗證數據的有效性
        if (saved && saved >= 1 && saved <= courseSteps.length) {
          setCurrentStep(saved)
        }
        if (Array.isArray(completed)) {
          setCompletedSteps(completed.filter(step => step >= 1 && step <= courseSteps.length))
        }
      } catch (error) {
        console.warn('Failed to parse saved progress:', error)
        // 清除無效的本地存儲數據
        localStorage.removeItem('n8n-course-progress')
      }
    }
    
    // 增加開始課程統計
    incrementCourseStarted()
    setStats(getCourseStats())
  }, [isClient])

  // 保存進度到本地存儲
  useEffect(() => {
    if (!isClient) return
    
    localStorage.setItem('n8n-course-progress', JSON.stringify({
      currentStep,
      completed: completedSteps
    }))
  }, [currentStep, completedSteps, isClient])

  const progress = (completedSteps.length / courseSteps.length) * 100

  const handleStepComplete = () => {
    if (!completedSteps.includes(currentStep)) {
      setCompletedSteps([...completedSteps, currentStep])
    }
    
    if (currentStep < courseSteps.length) {
      setCurrentStep(currentStep + 1)
    } else {
      // 完成課程統計
      incrementCourseCompleted()
      setStats(getCourseStats())
      setShowUpgrade(true)
    }
  }

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const currentStepData = courseSteps.find(step => step.id === currentStep)

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50">
      {/* 導航欄 */}
      <CourseNav />
      
      {/* 課程頭部 */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Settings className="h-8 w-8 text-green-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  n8n自動化工具速成課
                </h1>
                <p className="text-gray-600">免費體驗 • 無需登錄</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Badge variant="secondary" className="text-green-700 bg-green-100">
                <Trophy className="w-4 h-4 mr-1" />
                {completedSteps.length}/{courseSteps.length} 已完成
              </Badge>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-gray-600">
              <span>課程進度</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        </div>
      </div>

      {/* 主要內容區域 */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* 左側：課程大綱 */}
          <div className="lg:col-span-1">
            <Card className="p-4 sticky top-4">
              <h3 className="font-semibold mb-4 text-gray-900">課程大綱</h3>
              <div className="space-y-2">
                {courseSteps.map((step) => (
                  <button
                    key={step.id}
                    onClick={() => setCurrentStep(step.id)}
                    className={`w-full text-left p-3 rounded-lg transition-colors ${
                      currentStep === step.id
                        ? 'bg-green-100 text-green-700 border border-green-200'
                        : 'hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      {completedSteps.includes(step.id) ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <div className={`w-5 h-5 rounded-full border-2 ${
                          currentStep === step.id ? 'border-green-500' : 'border-gray-300'
                        }`} />
                      )}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          第{step.id}課
                        </p>
                        <p className="text-xs text-gray-500 flex items-center">
                          <Clock className="w-3 h-3 mr-1" />
                          {step.duration}
                        </p>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </Card>
          </div>

          {/* 右側：課程內容 */}
          <div className="lg:col-span-3">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="p-8">
                  {currentStepData && (
                    <>
                      <div className="mb-6">
                        <Badge className="mb-3">
                          第 {currentStep} 課 · {currentStepData.duration}
                        </Badge>
                        <h2 className="text-2xl font-bold text-gray-900 mb-4">
                          {currentStepData.title}
                        </h2>
                        <p className="text-gray-600 leading-relaxed">
                          {currentStepData.content.description}
                        </p>
                      </div>

                      {/* 根據不同類型渲染不同內容 */}
                      {currentStepData.type === 'video' && (
                        <div className="space-y-6">
                          <div className="bg-gray-100 rounded-lg p-8 text-center">
                            <Play className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                            <p className="text-gray-600">視頻播放器佔位</p>
                            <p className="text-sm text-gray-500 mt-2">
                              實際項目中這裡會是n8n操作演示視頻
                            </p>
                          </div>
                          <div>
                            <h4 className="font-semibold mb-3">本節要點：</h4>
                            <ul className="space-y-2">
                              {currentStepData.content.keyPoints?.map((point, index) => (
                                <li key={index} className="flex items-start">
                                  <Target className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                                  <span>{point}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}

                      {currentStepData.type === 'interactive' && (
                        <div className="space-y-6">
                          {currentStepData.content.principles?.map((principle, index) => (
                            <Card key={index} className="p-6 border-l-4 border-l-green-500">
                              <h4 className="font-bold text-lg mb-2">{principle.title}</h4>
                              <p className="text-gray-600 mb-3">{principle.description}</p>
                              <div className="bg-green-50 p-4 rounded-lg">
                                <p className="text-sm font-medium text-green-800">示例：</p>
                                <p className="text-green-700">{principle.example}</p>
                              </div>
                            </Card>
                          ))}
                        </div>
                      )}

                      {currentStepData.type === 'practice' && (
                        <div className="space-y-6">
                          {currentStepData.content.cases?.map((caseItem, index) => (
                            <Card key={index} className="p-6 border-l-4 border-l-blue-500">
                              <div className="flex items-center mb-4">
                                <Badge variant="outline" className="mr-3">
                                  {caseItem.industry}
                                </Badge>
                                <h4 className="font-bold text-lg">{caseItem.company}</h4>
                              </div>
                              
                              {/* 要點概覽 */}
                              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                                {caseItem.highlights.map((highlight, hIndex) => (
                                  <div key={hIndex} className="bg-green-50 p-3 rounded-lg">
                                    <Workflow className="h-4 w-4 text-green-600 mb-2" />
                                    <p className="text-sm font-medium text-green-800">
                                      {highlight}
                                    </p>
                                  </div>
                                ))}
                              </div>

                              {/* 詳細步驟 */}
                              {caseItem.detailedSteps && (
                                <div className="mb-6">
                                  <h5 className="font-semibold text-gray-900 mb-3 flex items-center">
                                    <Target className="h-4 w-4 text-blue-600 mr-2" />
                                    操作步驟
                                  </h5>
                                  <ol className="space-y-2 ml-6">
                                    {caseItem.detailedSteps.map((step, sIndex) => (
                                      <li key={sIndex} className="flex items-start">
                                        <span className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white text-xs font-bold rounded-full flex items-center justify-center mr-3 mt-0.5">
                                          {sIndex + 1}
                                        </span>
                                        <span className="text-gray-700">{step}</span>
                                      </li>
                                    ))}
                                  </ol>
                                </div>
                              )}

                              {/* 關鍵功能 */}
                              {caseItem.keyFeatures && (
                                <div className="bg-blue-50 p-4 rounded-lg">
                                  <h5 className="font-semibold text-blue-900 mb-3 flex items-center">
                                    <Star className="h-4 w-4 text-blue-600 mr-2" />
                                    關鍵功能說明
                                  </h5>
                                  <ul className="space-y-2">
                                    {caseItem.keyFeatures.map((feature, fIndex) => (
                                      <li key={fIndex} className="flex items-start">
                                        <CheckCircle className="h-4 w-4 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                                        <span className="text-blue-800 text-sm">{feature}</span>
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                            </Card>
                          ))}
                        </div>
                      )}

                      {currentStepData.type === 'tools' && (
                        <div className="space-y-4">
                          {currentStepData.content.tools?.map((tool, index) => (
                            <Card key={index} className="p-6">
                              <div className="flex justify-between items-start mb-4">
                                <div>
                                  <h4 className="font-bold text-lg">{tool.name}</h4>
                                  <Badge variant="secondary" className="mt-1">
                                    {tool.type}
                                  </Badge>
                                </div>
                                <div className="text-right">
                                  <p className="font-semibold text-green-600">{tool.price}</p>
                                </div>
                              </div>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                  <p className="font-medium mb-2">主要功能：</p>
                                  <ul className="text-sm space-y-1">
                                    {tool.features.map((feature, fIndex) => (
                                      <li key={fIndex} className="flex items-center">
                                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                                        {feature}
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                                <div>
                                  <p className="font-medium mb-2">適用場景：</p>
                                  <p className="text-sm text-gray-600">{tool.suitable}</p>
                                </div>
                              </div>
                            </Card>
                          ))}
                        </div>
                      )}

                      {currentStepData.type === 'summary' && (
                        <div className="space-y-6">
                          <Card className="p-6 bg-gradient-to-r from-green-50 to-blue-50">
                            <h4 className="font-bold text-lg mb-4 text-green-900">
                              你的n8n學習行動計劃
                            </h4>
                            <ol className="space-y-3">
                              {currentStepData.content.actionPlan?.map((step, index) => (
                                <li key={index} className="flex items-start">
                                  <span className="flex-shrink-0 w-6 h-6 bg-green-600 text-white text-sm font-bold rounded-full flex items-center justify-center mr-3 mt-0.5">
                                    {index + 1}
                                  </span>
                                  <span className="text-green-800">{step}</span>
                                </li>
                              ))}
                            </ol>
                          </Card>
                          <Card className="p-6 border-l-4 border-l-green-500">
                            <h4 className="font-bold text-lg mb-3 text-green-800">
                              繼續學習n8n
                            </h4>
                            <p className="text-gray-700 mb-4">
                              {currentStepData.content.nextSteps}
                            </p>
                            <Button asChild className="bg-green-600 hover:bg-green-700">
                              <Link href="/n8n">
                                了解n8n專題課程
                                <ArrowRight className="ml-2 h-4 w-4" />
                              </Link>
                            </Button>
                          </Card>
                        </div>
                      )}

                      {/* 課程導航 */}
                      <div className="flex justify-between items-center mt-8 pt-6 border-t">
                        <Button
                          variant="outline"
                          onClick={handlePrevStep}
                          disabled={currentStep === 1}
                          className="flex items-center"
                        >
                          <ArrowLeft className="mr-2 h-4 w-4" />
                          上一課
                        </Button>

                        <div className="flex items-center space-x-2 text-sm text-gray-500">
                          <Users className="h-4 w-4" />
                          {isClient ? (
                            <span>已有 {formatNumber(stats.totalStarted)} 人學習了這門課程</span>
                          ) : (
                            <span>載入中...</span>
                          )}
                        </div>

                        <Button
                          onClick={handleStepComplete}
                          className="flex items-center bg-green-600 hover:bg-green-700"
                        >
                          {currentStep === courseSteps.length ? '完成課程' : '下一課'}
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </>
                  )}
                </Card>
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>

      {/* 升級彈窗 */}
      <AnimatePresence>
        {showUpgrade && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowUpgrade(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-lg p-8 max-w-md w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center">
                <Trophy className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
                <h3 className="text-xl font-bold mb-2">恭喜完成n8n基礎課程！</h3>
                <p className="text-gray-600 mb-6">
                  你已經掌握了n8n自動化工具的基礎知識。準備好深入學習更高級的自動化技巧了嗎？
                </p>
                <div className="space-y-3">
                  <Button asChild className="w-full bg-green-600 hover:bg-green-700">
                    <Link href="/n8n">
                      了解n8n專題課程
                    </Link>
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => setShowUpgrade(false)}
                  >
                    稍後再說
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
} 