'use client'

import { useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { useAuth } from '@/auth/contexts/simplified-auth-context'
import { useAuthNavigation } from '@/auth/hooks/use-auth-navigation'
import { LoginForm } from '@/components/auth/login-form'
import { AuthErrorReset } from '@/components/auth/auth-error-reset'
import { useToast } from '@/components/ui/use-toast'

export default function LoginPage() {
  const searchParams = useSearchParams()
  const { login, error, isAuthenticated } = useAuth()
  const { toast } = useToast()
  const { 
    isLoading, 
    navigateAfterAuth 
  } = useAuthNavigation()
  
  // 检查是否处于错误状态
  const isErrorState = !!error
  
  useEffect(() => {
    const returnUrl = searchParams.get('returnUrl')
    
    // 如果目标是 auth-rules 页面，直接跳转
    if (returnUrl === '/auth-rules') {
      window.location.href = '/auth-rules'
      return
    }

    if (isLoading) {
      console.log('[LoginPage] 正在加载中，跳过导航')
      return
    }

    if (isAuthenticated) {
      console.log('[LoginPage] 准备重定向', {
        returnUrl,
        targetUrl: returnUrl || '/dashboard',
        isClient: true,
        isLoading,
        currentTime: new Date().toISOString()
      })
      navigateAfterAuth(returnUrl || '/dashboard')
    }
  }, [isAuthenticated, isLoading, searchParams, navigateAfterAuth])

  const handleLogin = async (values: { email: string; password: string }) => {
    try {
      console.log('[LoginPage] 开始登录', {
        email: values.email,
        currentTime: new Date().toISOString()
      })
      
      await login(values.email, values.password)
      
      console.log('[LoginPage] 登录成功，等待状态更新和导航', {
        currentTime: new Date().toISOString()
      })

      toast({
        title: '登录成功',
        description: '欢迎回来！',
      })
    } catch (error) {
      console.error('[LoginPage] 登录失败:', error)
      toast({
        title: '登录失败',
        description: error instanceof Error ? error.message : '请稍后重试',
        variant: 'destructive',
      })
      throw error
    }
  }
  
  // 处理状态重置
  const handleStateReset = () => {
    console.log('[LoginPage] 用户手动重置认证状态', {
      previousState: error,
      timestamp: new Date().toISOString()
    })
    
    toast({
      title: '认证状态已重置',
      description: '系统将在几秒后刷新...',
    })
  }

  return (
    <div className="container relative min-h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      <div className="relative hidden h-full flex-col bg-muted p-10 text-white lg:flex dark:border-r">
        <div className="absolute inset-0 bg-zinc-900" />
        <div className="relative z-20 flex items-center text-lg font-medium">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="mr-2 h-6 w-6"
          >
            <path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3" />
          </svg>
          How to AI｜最實用的AI應用課
        </div>
        <div className="relative z-20 mt-auto">
          <blockquote className="space-y-2">
            <p className="text-lg">
              最貼近實際需求的AI應用實戰課程，涵蓋ChatGPT、AI繪圖、辦公自動化等熱門主題。無需技術背景，學完即用，讓AI幫你提升工作與生活效率！
            </p>
          </blockquote>
        </div>
      </div>
      <div className="lg:p-8">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          <div className="flex flex-col space-y-2 text-center">
            <h1 className="text-2xl font-semibold tracking-tight">
              登入帳號
            </h1>
            <p className="text-sm text-muted-foreground">
              請輸入您的信箱與密碼
            </p>
          </div>
          
          {/* 错误状态提示和重置按钮 */}
          {isErrorState && (
            <AuthErrorReset onReset={handleStateReset} />
          )}
          
          <LoginForm onSubmit={handleLogin} />
          <p className="px-8 text-center text-sm text-muted-foreground">
            還沒有帳號？{' '}
            <a
              href="/register"
              className="underline underline-offset-4 hover:text-primary"
            >
              立即註冊
            </a>
          </p>
        </div>
      </div>
    </div>
  )
} 