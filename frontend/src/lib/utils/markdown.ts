import { CourseStep } from '@/types/course';

// 获取后端 API URL
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

export function parseMarkdownToSteps(markdown: string): CourseStep[] {
  console.log('开始解析课程内容:', markdown);
  const steps: CourseStep[] = [];
  const lines = markdown.split('\n');
  let currentContent = '';

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // 跳过空行
    if (!line) continue;

    // 处理图片练习
    if (line.startsWith('[practice:image')) {
      console.log('发现图片练习开始');
      if (currentContent) {
        steps.push(parseDialogue(currentContent));
        currentContent = '';
      }

      let practiceContent = line;
      let practiceEndFound = false;
      
      while (i < lines.length - 1) {
        i++;
        practiceContent += '\n' + lines[i];
        if (lines[i].includes('[/practice]')) {
          practiceEndFound = true;
          break;
        }
      }

      if (practiceEndFound) {
        console.log('完整的图片练习内容:', practiceContent);
        steps.push(parseImagePractice(practiceContent));
      }
      continue;
    }

    // 处理对话练习
    if (line.startsWith('[practice:chat')) {
      if (currentContent) {
        steps.push(parseDialogue(currentContent));
        currentContent = '';
      }

      let practiceContent = line;
      let practiceEndFound = false;

      while (i < lines.length - 1) {
        i++;
        practiceContent += '\n' + lines[i];
        if (lines[i].includes('[/practice]')) {
          practiceEndFound = true;
          break;
        }
      }

      if (practiceEndFound) {
        steps.push(parseChatPractice(practiceContent));
      }
      continue;
    }

    // 处理等待
    if (line.startsWith('[等待')) {
      if (currentContent) {
        steps.push(parseDialogue(currentContent));
        currentContent = '';
      }
      steps.push({
        type: 'wait',
        content: line.replace(/\[等待([^\]]*)\]/, '$1').trim()
      });
      continue;
    }

    // 处理对话
    if (line.startsWith('[') && line.includes(']:')) {
      if (currentContent) {
        steps.push(parseDialogue(currentContent));
        currentContent = '';
      }
    }

    currentContent += (currentContent ? '\n' : '') + line;
  }

  if (currentContent) {
    steps.push(parseDialogue(currentContent));
  }

  console.log('解析完成，生成步骤数:', steps.length);
  return steps;
}

function parseDialogue(content: string): CourseStep {
  const match = content.match(/\[([^\]]+)\]:\s*([\s\S]*)/);
  if (!match) {
    return {
      type: 'dialogue',
      speaker: '系统',
      content: content.trim()
    };
  }

  return {
    type: 'dialogue',
    speaker: match[1],
    content: match[2].trim()
  };
}

function parseChatPractice(content: string): CourseStep {
  const modeMatch = content.match(/mode="([^"]+)"/);
  const practiceContent = content
    .replace(/\[practice:chat[^\]]*\]/, '')
    .replace(/\[\/practice\]/, '')
    .trim();

  return {
    type: 'ai_practice',
    content: practiceContent,
    practice: {
      type: 'chat',
      mode: modeMatch ? modeMatch[1] : 'interactive'
    }
  };
}

function parseImagePractice(content: string): CourseStep {
  console.log('解析图片练习内容:', content);
  
  const modeMatch = content.match(/mode="([^"]+)"/);
  const sizeMatch = content.match(/size="([^"]+)"/);
  
  const practiceContent = content
    .replace(/\[practice:image[^\]]*\]/, '')
    .replace(/\[\/practice\]/, '')
    .trim();

  const result: CourseStep = {
    type: 'image_practice',
    content: practiceContent,
    image_practice: {
      mode: modeMatch ? modeMatch[1] : 'create',
      size: sizeMatch ? sizeMatch[1] : '1024x1024'
    }
  };

  console.log('解析后的图片练习步骤:', result);
  return result;
} 