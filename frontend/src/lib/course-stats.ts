// 課程統計數據管理
interface CourseStats {
  totalStarted: number;
  totalCompleted: number;
  completionRate: number;
}

// 模擬的統計數據，實際項目中這些數據應該來自後端API
const MOCK_STATS: CourseStats = {
  totalStarted: 2847,
  totalCompleted: 1923,
  completionRate: 67.5
}

// 獲取課程統計數據
export function getCourseStats(): CourseStats {
  // 從localStorage獲取本地統計
  const localStats = getLocalStats()
  
  return {
    totalStarted: MOCK_STATS.totalStarted + localStats.started,
    totalCompleted: MOCK_STATS.totalCompleted + localStats.completed,
    completionRate: MOCK_STATS.completionRate
  }
}

// 增加開始課程的統計數
export function incrementCourseStarted() {
  const stats = getLocalStats()
  const newStats = {
    ...stats,
    started: stats.started + 1
  }
  
  localStorage.setItem('course-local-stats', JSON.stringify(newStats))
}

// 增加完成課程的統計數
export function incrementCourseCompleted() {
  const stats = getLocalStats()
  const newStats = {
    ...stats,
    completed: stats.completed + 1
  }
  
  localStorage.setItem('course-local-stats', JSON.stringify(newStats))
}

// 獲取本地統計數據
function getLocalStats() {
  if (typeof window === 'undefined') {
    return { started: 0, completed: 0 }
  }
  
  const saved = localStorage.getItem('course-local-stats')
  if (saved) {
    try {
      return JSON.parse(saved)
    } catch {
      return { started: 0, completed: 0 }
    }
  }
  
  return { started: 0, completed: 0 }
}

// 格式化數字顯示
export function formatNumber(num: number): string {
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
} 