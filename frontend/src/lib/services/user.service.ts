import { api } from '@/lib/api/index';

export enum UserRole {
  USER = 'user',
  ADMIN = 'admin',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export interface User {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  role: UserRole;
  status: UserStatus;
  isActive: boolean;
}

export interface CreateUserDto {
  email: string;
  name: string;
  password: string;
  role?: UserRole;
}

export interface UpdateUserDto {
  name?: string;
  role?: UserRole;
  status?: UserStatus;
}

export interface UpdatePasswordDto {
  oldPassword: string;
  newPassword: string;
}

export class UserService {
  static async getUsers(): Promise<User[]> {
    const response = await api.get('/api/admin/users');
    return response.data;
  }

  static async getUser(id: string): Promise<User> {
    const response = await api.get(`/api/admin/users/${id}`);
    return response.data;
  }

  static async createUser(data: CreateUserDto): Promise<User> {
    const response = await api.post('/api/admin/users', data);
    return response.data;
  }

  static async updateUser(id: string, data: UpdateUserDto): Promise<User> {
    const response = await api.patch(`/api/admin/users/${id}`, data);
    return response.data;
  }

  static async updatePassword(data: UpdatePasswordDto): Promise<void> {
    await api.post('/api/users/password', data);
  }

  static async deleteUser(id: string): Promise<void> {
    await api.delete(`/api/admin/users/${id}`);
  }

  static async getProfile(): Promise<User> {
    const response = await api.get('/api/users/profile');
    return response.data;
  }

  static async updateProfile(data: {
    name?: string;
    avatar?: string;
  }): Promise<User> {
    const response = await api.patch('/api/users/profile', data);
    return response.data;
  }
} 