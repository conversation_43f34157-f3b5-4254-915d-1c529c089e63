# 环境设置
NODE_ENV=development

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=ailessonsuser
DB_PASSWORD=ailessons123456
DB_DATABASE=ailessons
DB_SYNCHRONIZE=false
DB_LOGGING=false

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT配置
JWT_SECRET=trading_lessons_development_secret_key_2024
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=trading_lessons_development_refresh_secret_key_2024
JWT_REFRESH_EXPIRES_IN=30d

# API 配置
PORT=3001

# Cookie 配置
COOKIE_DOMAIN=localhost
FRONTEND_URL=http://localhost:3000 

STRIPE_SECRET_KEY=sk_test_51RHh9RQLbVN3sXcIpL93XjNMAewOiLnPiBup0RvlZhSZ11KNWP6DcWEsQ5kVBxEBxXm8cy8my4d5wDQhk7WByTIO009XZXxPaD
STRIPE_PUBLISHABLE_KEY=pk_test_51RHh9RQLbVN3sXcIeUf8WOZuB78xVtEpHhXkSAjI7Qby8bAf3LcEwcAkme3yMozHQt9zPcRmSGdw8ME6KHyIlrin00laxvkibw
STRIPE_WEBHOOK_SECRET=whsec_e69ed774e1e9419f30ed6092bd26d061f7ac0c178b56836d6e9bc5d1174fc6ef