import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '@/modules/user/entities/user.entity';
import { AdminController } from './controllers/admin.controller';
import { AdminService } from './services/admin.service';
import { StatsService } from './services/stats.service';
import { AiModule } from '@/modules/ai/ai.module';
import { AiProviderController } from './controllers/ai-provider.controller';
import { AdminAiProviderService } from './services/ai-provider.service';
import { AiProvider } from '@/modules/ai/entities/ai-provider.entity';
import { AdminUsersController } from './controllers/admin-users.controller';
import { AdminUsersService } from './services/admin-users.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, AiProvider]),
    AiModule,
  ],
  controllers: [
    <PERSON>min<PERSON>ontroller,
    Ai<PERSON>roviderController,
    AdminUsersController,
  ],
  providers: [
    AdminService,
    StatsService,
    AdminAiProviderService,
    AdminUsersService,
  ],
})
export class AdminModule {} 