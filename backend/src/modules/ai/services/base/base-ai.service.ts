import { Logger } from '@nestjs/common';
import { AiServiceOptions, AiServiceResponse, IAiService, AiEditParams, DescriptionCompleteParams, ImageGenerateResponse, ImageGenerateOptions } from '../../interfaces/ai-service.interface';
import { AiProviderService } from '../ai-provider.service';
import { AiProvider } from '../../entities/ai-provider.entity';
import { PromptManager, CourseGeneratorParams, QuizParams } from '../prompt/prompt-manager';

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface ChatParams {
  messages: ChatMessage[];
  character?: string;
  temperature?: number;
  max_tokens?: number;
}

export interface ImageGenerationOptions {
  prompt: string;
  negativePrompt?: string;
  size?: string;
  steps?: number;
  guidance?: number;
  seed?: number;
}

// 定义角色提示词映射类型
type CharacterPrompts = {
  [key: string]: string;
  '助教': string;
  'assistant': string;
};

export abstract class BaseAiService implements IAiService {
  protected readonly logger: Logger;
  protected options: AiServiceOptions;
  protected lastUsedAt?: Date;
  protected initialized = false;

  // 角色提示词映射
  protected readonly characterPrompts: CharacterPrompts = {
    '助教': '你是一位专业、友善的助教，擅长引导学习和解答问题。请用简洁清晰的语言回答问题，必要时给出示例。',
    'assistant': `你是一位耐心的AI助教。你需要对学生的答案进行点评。
      请从以下几个方面进行评价：
      1. 答案的准确性和完整性
      2. 思路是否清晰
      3. 有哪些可以改进的地方
      4. 给出改进建议和鼓励
      请用友善的语气点评。`
  };

  constructor(
    protected readonly promptManager: PromptManager,
    protected readonly aiProviderService: AiProviderService,
    protected readonly provider: AiProvider,
    loggerContext?: string,
  ) {
    this.logger = new Logger(loggerContext || this.constructor.name);
  }

  async initialize(options: AiServiceOptions): Promise<void> {
    try {
      this.options = options;
      this.logger.log(`Initializing AI service with provider: ${options.provider.name}`);
      await this.initializeClient();
      this.initialized = true;
      this.logger.log('AI service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize AI service:', error);
      throw error;
    }
  }

  protected getSystemPrompt(): string {
    return `你是一个专业的儿童绘本插画提示词生成助手。你擅长将场景描述转换为清晰、具体的英文插画提示词。

提示词要求：
1. 简洁明了，突出场景的关键视觉元素和氛围
2. 对于角色描述：
   - 必须准确体现角色的类型（如：猫、狗、兔子等）
   - 详细描述角色的外观特征（如：毛色、体型、特征等）
   - 通过肢体语言和表情展现角色的性格特征
   - 确保角色在整个场景中的表现符合其设定
3. 场景描述要生动具体，包含环境细节
4. 生成的英文提示词要确保场景和角色的视觉一致性
5. 适合儿童绘本的温暖友好风格`;
  }

  protected formatUserPrompt(params: AiEditParams): string {
    const { content, instruction, style, character } = params;
    let prompt = `${instruction}\n\n`;

    if (style) {
      prompt += `风格：${style}\n\n`;
    }

    if (character) {
      prompt += `主角信息：
- 名字：${character.name}
- 类型：${character.type}${character.appearance ? `\n- 外观：${character.appearance}` : ''}${character.personality ? `\n- 性格：${character.personality}` : ''}${character.description ? `\n- 补充描述：${character.description}` : ''}\n\n`;
    }

    if (content) {
      prompt += `场景：${content}`;
    }

    return prompt;
  }

  protected formatScenePrompt(
    theme: string,
    description: string,
    sceneCount: number,
    wordCount: 'single' | '2-10' | '10-30' | '30-80' | '80-200',
    keyWords?: string,
    subjectKnowledge?: string,
    vocabularyLevel?: string,
    mainCharacter?: string,
  ): string {
    // 根据词汇量级别调整提示内容
    const isZeroLevel = vocabularyLevel === 'zero';
    const isSingleWord = wordCount === 'single';
    
    let specialInstructions = '';
    if (isZeroLevel || isSingleWord) {
      specialInstructions = `
特殊要求：
1. 每个场景文本只使用1个简单的中文词语或短语
2. 使用要求学习的词汇`;
    } else {
      const [min, max] = wordCount.split('-').map(Number);
      specialInstructions = `
特殊要求：
1. 每个场景使用${min}到${max}个中文词语
2. 确保句子结构简单清晰
3. 优先使用常见词汇
4. 适当重复关键词以加深印象
5. 内容要生动有趣`;
    }

    const baseInfo = `基本信息：
主题：${theme}
${description ? `描述：${description}` : ''}
${mainCharacter ? `主角：${mainCharacter}` : ''}
场景包含的词语数量：${wordCount === 'single' ? '每个场景1个词语' : wordCount + '个词语'}
${keyWords ? `重点词语：${keyWords}` : ''}
${subjectKnowledge ? `知识点：${subjectKnowledge}` : ''}
${vocabularyLevel ? `词汇量：${vocabularyLevel}` : ''}`;

    return `请为一本中文绘本生成${sceneCount}个场景的文本部分内容，用于绘本的文字部分。

${baseInfo}

${specialInstructions}

请按照以下格式返回场景：
场景1:
[场景1的中文内容]

场景2:
[场景2的中文内容]

...以此类推`;
  }

  protected abstract initializeClient(): Promise<void>;

  protected async sendRequest(messages: ChatMessage[]): Promise<AiServiceResponse<string>> {
    try {
      const response = await this.makeRequest(messages);
      
      if (!response.content) {
        return {
          success: false,
          error: 'AI service returned empty content',
          metadata: {
            provider: this.options?.provider?.type,
            timestamp: new Date().toISOString()
          }
        };
      }

      const tokens = response.totalTokens || 0;
      const cost = this.calculateCost(tokens);

      return {
        success: true,
        data: response.content,
        usage: {
          totalTokens: tokens,
          cost,
        },
        metadata: {
          provider: this.options?.provider?.type,
          model: this.options?.provider?.config?.modelName,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  protected abstract makeRequest(messages: ChatMessage[]): Promise<{
    content: string;
    totalTokens: number;
  }>;

  protected abstract calculateCost(tokens: number): number;

  async editContent(params: AiEditParams): Promise<AiServiceResponse<string>> {
    try {
      if (!this.initialized) {
        await this.initialize(this.options);
      }

      const messages: ChatMessage[] = [
        { role: 'system' as const, content: this.getSystemPrompt() },
        { role: 'user' as const, content: this.formatUserPrompt(params) },
      ];

      return await this.sendRequest(messages);
    } catch (error) {
      return this.handleError(error);
    }
  }

  async generateScenes(
    theme: string,
    description: string,
    sceneCount: number,
    wordCount: 'single' | '2-10' | '10-30' | '30-80' | '80-200',
    keyWords?: string,
    subjectKnowledge?: string,
    vocabularyLevel?: string,
    mainCharacter?: string,
  ): Promise<AiServiceResponse<string>> {
    try {
      if (!this.initialized) {
        await this.initializeClient();
        this.initialized = true;
      }

      const prompt = this.formatScenePrompt(
        theme,
        description,
        sceneCount,
        wordCount,
        keyWords,
        subjectKnowledge,
        vocabularyLevel,
        mainCharacter,
      );

      this.logger.log('=== 生成的场景 Prompt ===');
      this.logger.log(prompt);

      const messages: ChatMessage[] = [
        {
          role: 'system' as const,
          content: this.getSystemPrompt(),
        },
        {
          role: 'user' as const,
          content: prompt,
        },
      ];

      return await this.sendRequest(messages);
    } catch (error) {
      return this.handleError(error);
    }
  }

  async checkStatus(): Promise<boolean> {
    try {
      const provider = this.options?.provider;
      if (!provider) {
        return false;
      }
      return provider.status === 'active';
    } catch (error) {
      this.logger.error('Error checking status:', error);
      return false;
    }
  }

  protected async updateUsageStats(tokens: number, cost: number): Promise<void> {
    // 暂时禁用统计功能
    return;
  }

  protected handleError(error: any): AiServiceResponse<string> {
    this.logger.error('AI service error:', error);
    return {
      success: false,
      error: error.message || 'Unknown error occurred',
      metadata: {
        provider: this.options?.provider?.type,
        timestamp: new Date().toISOString()
      }
    };
  }

  async completeDescription(params: DescriptionCompleteParams): Promise<AiServiceResponse<string>> {
    try {
      if (!this.initialized) {
        await this.initialize(this.options);
      }

      const messages: ChatMessage[] = [
        { role: 'system' as const, content: this.getSystemPrompt() },
        { role: 'user' as const, content: this.formatDescriptionPrompt(params) },
      ];

      return await this.sendRequest(messages);
    } catch (error) {
      return this.handleError(error);
    }
  }

  protected formatDescriptionPrompt(params: DescriptionCompleteParams): string {
    const { type, currentDescription, characterType, species } = params;
    
    // 根据类型设置不同的系统提示词
    const systemPrompt = type === 'appearance' 
      ? `你是一个专业的儿童绘本角色外观设计助手。你的任务是基于已有的简单外观描述，补充更丰富的外观细节。

角色基本信息：
${characterType ? `角色类型：${characterType === 'human' ? '人类' : characterType === 'animal' ? '动物' : characterType === 'fantasy' ? '幻想生物' : '其他'}\n` : ''}${species ? `具体物种：${species}\n` : ''}

要求：
1. 使用生动形象的中文描述
2. 保持友好温暖的风格，适合儿童阅读
3. 描述要具体且富有画面感
4. 保持原有描述的基本特征，在此基础上进行合理扩展
5. 返回的内容应该是完整的描述，而不是仅仅是补充的部分
6. 仅描述角色的外观特征，包括：
   - 体型特征（高矮胖瘦、大小等）
   - 外表特征（毛发、皮肤、眼睛等）
   - 标志性特征（特殊标记、装扮等）
7. 不要包含动作描写、场景描写或性格描写`
      : `你是一个专业的儿童绘本角色性格设计助手。你的任务是基于已有的简单性格描述，补充更丰富的性格细节。

角色基本信息：
${characterType ? `角色类型：${characterType === 'human' ? '人类' : characterType === 'animal' ? '动物' : characterType === 'fantasy' ? '幻想生物' : '其他'}\n` : ''}${species ? `具体物种：${species}\n` : ''}

要求：
1. 使用生动形象的中文描述
2. 保持友好温暖的风格，适合儿童阅读
3. 描述要具体且富有画面感
4. 保持原有描述的基本特征，在此基础上进行合理扩展
5. 返回的内容应该是完整的描述，而不是仅仅是补充的部分
6. 仅描述角色的性格特点，包括：
   - 基本性格（开朗、内向等）
   - 情感特征（温柔、善良等）
   - 行为倾向（喜欢帮助他人等）
7. 不要包含外观描写、动作描写或场景描写`;

    const promptTemplates: Record<DescriptionCompleteParams['type'], string> = {
      appearance: '基于已有的外貌描述，补充更丰富的外观细节（不要包含动作和场景描写）：',
      personality: '基于已有的性格描述，补充更丰富的性格特点（不要包含外观和动作描写）：'
    };

    return `${systemPrompt}\n\n${promptTemplates[type]}\n${currentDescription || ''}`;
  }

  getConfig(): AiServiceOptions {
    return this.options;
  }

  async updateConfig(options: Partial<AiServiceOptions>): Promise<void> {
    this.options = { ...this.options, ...options };
  }

  async getStats(): Promise<{ requestCount: number; costAmount: number; lastUsedAt?: Date }> {
    const provider = this.options?.provider;
    if (!provider) {
      return { requestCount: 0, costAmount: 0 };
    }
    return {
      requestCount: provider.requestCount || 0,
      costAmount: Number(provider.costAmount || 0),
      lastUsedAt: this.lastUsedAt,
    };
  }

  async chat(params: ChatParams): Promise<string> {
    try {
      if (!this.initialized) {
        await this.initializeClient();
      }
      const messages = this.formatChatPrompt(params);
      const response = await this.makeRequest(messages);
      return response.content;
    } catch (error) {
      this.logger.error('AI chat failed:', error);
      throw error;
    }
  }

  protected formatChatPrompt(params: ChatParams): ChatMessage[] {
    const { messages, character } = params;
    
    // 添加角色设定
    const systemMessage: ChatMessage = {
      role: 'system' as const,
      content: this.getCharacterPrompt(character),
    };

    // 确保所有消息都符合 ChatMessage 类型
    const typedMessages: ChatMessage[] = messages.map(msg => ({
      role: msg.role as 'system' | 'user' | 'assistant',
      content: msg.content
    }));

    return [systemMessage, ...typedMessages];
  }

  protected getCharacterPrompt(character: string = '助教'): string {
    return this.characterPrompts[character as keyof CharacterPrompts] || this.characterPrompts['助教'];
  }

  async generateImage(options: ImageGenerateOptions): Promise<ImageGenerateResponse> {
    throw new Error('Image generation is not supported by this service');
  }

  // 业务方法
  async generateCourse(params: CourseGeneratorParams): Promise<AiServiceResponse<string>> {
    try {
      if (!this.initialized) {
        await this.initialize(this.options);
      }

      const systemPrompt = this.promptManager.getSystemPrompt('course');
      const userPrompt = this.promptManager.formatCoursePrompt(params);
      
      const messages: ChatMessage[] = [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ];

      return this.sendRequest(messages);
    } catch (error) {
      return this.handleError(error);
    }
  }

  async generateQuiz(params: QuizParams): Promise<AiServiceResponse<string>> {
    try {
      if (!this.initialized) {
        await this.initialize(this.options);
      }

      const systemPrompt = this.promptManager.getSystemPrompt('quiz');
      const userPrompt = this.promptManager.formatQuizPrompt(params);
      
      const messages: ChatMessage[] = [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ];

      return this.sendRequest(messages);
    } catch (error) {
      return this.handleError(error);
    }
  }
}