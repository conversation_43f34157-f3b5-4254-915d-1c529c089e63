import { Injectable } from '@nestjs/common';

export interface CourseGeneratorParams {
  title: string;
  rawMaterial: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  targetAudience: string;
  sectionCount?: number;
  interactionCount?: number;
  includeExercises?: boolean;
  tone?: 'formal' | 'casual';
}

export interface QuizParams {
  topic: string;
  difficulty: string;
  questionCount: number;
  type?: 'multiple_choice' | 'true_false' | 'open_ended';
}

@Injectable()
export class PromptManager {
  private systemPrompts: { [key: string]: string } = {
    courseGenerator: `你是一个专业的课程设计专家，擅长创建交互式学习内容。
请按照以下原则设计课程：
1. 内容要循序渐进
2. 使用对话形式展示内容
3. 适当加入互动练习
4. 确保专业性和趣味性平衡
5. 针对目标受众调整难度`,
    
    quiz: `你是一个专业的测试题生成助手。请根据提供的参数生成高质量的测试题。
生成的内容应该：
1. 难度适中
2. 题目清晰明确
3. 涵盖关键知识点
4. 包含标准答案和解释`,
  };

  getSystemPrompt(type: string): string {
    return this.systemPrompts[type] || '';
  }

  formatCoursePrompt(params: CourseGeneratorParams): string {
    const { title, difficulty, targetAudience, interactionCount, includeExercises } = params;
    
    let prompt = `请生成一个关于 "${title}" 的课程内容\n\n`;
    prompt += `难度级别：${difficulty}\n`;
    prompt += `目标受众：${targetAudience}\n`;
    
    if (interactionCount) {
      prompt += `交互轮数：${interactionCount}\n`;
    }
    
    if (includeExercises) {
      prompt += `包含练习：是\n`;
    }

    return prompt;
  }

  formatQuizPrompt(params: QuizParams): string {
    const { topic, difficulty, questionCount, type } = params;
    
    let prompt = `请生成 ${questionCount} 道关于 "${topic}" 的测试题\n\n`;
    prompt += `难度级别：${difficulty}\n`;
    
    if (type) {
      prompt += `题目类型：${type}\n`;
    }

    return prompt;
  }

  generateOutlinePrompt(params: CourseGeneratorParams): string {
    return `请根据以下原始素材生成一个交互式课程大纲：

主题：${params.title}
难度：${params.difficulty}
目标受众：${params.targetAudience}
原始素材：${params.rawMaterial}

要求：
1. 将内容分为 ${params.sectionCount || '3-5'} 个主要部分
2. 每部分包含 2-3 个小节
3. 确保内容循序渐进
4. 适当安排互动环节
5. 返回的大纲格式如下：

# 课程标题

## 第一部分：标题
### 1.1 小节标题
### 1.2 小节标题

## 第二部分：标题
### 2.1 小节标题
### 2.2 小节标题

...以此类推`;
  }

  generateIntroductionPrompt(params: CourseGeneratorParams, outline: string): string {
    return `请根据以下课程大纲生成课程的导入部分：

${outline}

要求：
1. 使用[系统]和[讲师]的对话形式
2. 介绍课程的主要内容和学习目标
3. 说明课程的重要性和应用场景
4. 激发学习兴趣
5. 语气要${params.tone || 'casual'}且专业
6. 确保内容生动有趣

格式要求：
[系统]: 欢迎语和课程介绍

[讲师]: 详细说明和引导

注意：确保内容既专业又容易理解，适合${params.targetAudience}的水平。`;
  }

  generateSectionPrompt(params: CourseGeneratorParams, sectionTitle: string): string {
    return `请生成以下小节的交互式课程内容：

小节标题：${sectionTitle}
难度级别：${params.difficulty}
目标受众：${params.targetAudience}
原始素材：${params.rawMaterial}

要求：
1. 使用[系统]和[讲师]的对话形式
2. 每个概念都要通过对话来展开
3. 内容要循序渐进
4. 适当加入互动练习
5. 语气要${params.tone || 'casual'}
6. 每个重要概念后添加练习
7. 确保内容既专业又有趣

格式示例：
## 小节标题

[讲师]: 概念讲解和示例

[系统]: 补充说明或引导

[practice:chat mode="interactive"]
练习内容和要求
[/practice]

[等待点击继续]`;
  }

  generateSummaryPrompt(params: CourseGeneratorParams, outline: string): string {
    return `请根据以下课程大纲生成总结部分：

${outline}

要求：
1. 使用[系统]和[讲师]的对话形式
2. 总结课程的主要内容和关键点
3. 提供学习建议和后续学习方向
4. 鼓励学习者实践和应用
5. 语气要${params.tone || 'casual'}且专业

格式示例：
## 课程总结

[讲师]: 总结主要内容和关键点

[系统]: 补充建议和鼓励

[等待点击继续]`;
  }
} 