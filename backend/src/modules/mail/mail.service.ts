import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';

@Injectable()
export class MailService {
  private readonly logger = new Logger(MailService.name);
  private transporter: nodemailer.Transporter;

  constructor(private readonly configService: ConfigService) {
    // 初始化邮件发送器
    this.initializeTransporter();
  }

  private initializeTransporter() {
    const host = this.configService.get<string>('MAIL_HOST');
    const port = this.configService.get<number>('MAIL_PORT');
    const user = this.configService.get<string>('MAIL_USER');
    const pass = this.configService.get<string>('MAIL_PASSWORD');
    const secure = this.configService.get<boolean>('MAIL_SECURE', false);

    if (!host || !port || !user || !pass) {
      this.logger.warn('邮件服务配置不完整，邮件功能将不可用');
      return;
    }

    this.transporter = nodemailer.createTransport({
      host,
      port,
      secure,
      auth: {
        user,
        pass,
      },
    });

    this.logger.log('邮件服务初始化成功');
  }

  /**
   * 发送邮件
   * @param to 收件人
   * @param subject 主题
   * @param html 内容
   */
  async sendMail(to: string, subject: string, html: string): Promise<boolean> {
    if (!this.transporter) {
      this.logger.error('邮件服务未初始化，无法发送邮件');
      return false;
    }

    try {
      const from = `${this.configService.get<string>('MAIL_FROM_NAME', 'AI探索家')} <${this.configService.get<string>('MAIL_FROM', '<EMAIL>')}>`;
      
      await this.transporter.sendMail({
        from,
        to,
        subject,
        html,
      });

      this.logger.log(`邮件发送成功: ${to}`);
      return true;
    } catch (error) {
      this.logger.error(`邮件发送失败: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 发送验证码邮件
   * @param to 收件人
   * @param code 验证码
   */
  async sendVerificationCode(to: string, code: string): Promise<boolean> {
    const subject = '【AI探索家】邮箱验证码';
    const html = `
      <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
        <h2 style="color: #333; text-align: center;">AI探索家 - 邮箱验证码</h2>
        <div style="background-color: #f7f7f7; padding: 20px; border-radius: 5px; margin-top: 20px;">
          <p>您好，</p>
          <p>您正在注册 AI探索家 账号，请使用以下验证码完成邮箱验证：</p>
          <div style="background-color: #ffffff; padding: 15px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 5px; margin: 20px 0; border-radius: 5px; border: 1px dashed #ccc;">
            ${code}
          </div>
          <p>验证码有效期为 10 分钟，请勿将验证码泄露给他人。</p>
          <p>如果您没有进行此操作，请忽略此邮件。</p>
        </div>
        <div style="margin-top: 30px; text-align: center; color: #999; font-size: 12px;">
          <p>此邮件由系统自动发送，请勿直接回复。</p>
          <p>© ${new Date().getFullYear()} AI探索家 版权所有</p>
        </div>
      </div>
    `;

    return this.sendMail(to, subject, html);
  }
}
