import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { Order, Payment, Refund } from './entities';
import { OrderService } from './services/order.service';
import { StripeService } from './services/stripe.service';
import { PaymentController } from './controllers/payment.controller';
import { CourseModule } from '../course/course.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Order, Payment, Refund]),
    ConfigModule,
    CourseModule,
  ],
  controllers: [PaymentController],
  providers: [
    OrderService,
    StripeService,
  ],
  exports: [OrderService],
})
export class PaymentModule {}