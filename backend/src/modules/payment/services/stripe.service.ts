import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Stripe from 'stripe';
import { OrderService } from './order.service';

@Injectable()
export class StripeService {
  private stripe: Stripe | null = null;
  private readonly logger = new Logger(StripeService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly orderService: OrderService,
  ) {
    const apiKey = this.configService.get<string>('STRIPE_SECRET_KEY');
    if (!apiKey) {
      this.logger.warn('STRIPE_SECRET_KEY is not defined in environment variables, Stripe payment will be mocked');
    } else {
      try {
        this.stripe = new Stripe(apiKey, {
          apiVersion: '2025-05-28.basil',
        });
      } catch (error) {
        this.logger.error('Failed to initialize Stripe', {
          error: error instanceof Error ? error.message : error,
        });
      }
    }
  }

  /**
   * 创建支付意向
   */
  async createPaymentIntent(
    amount: number,
    currency: string = 'usd',
    metadata: Record<string, string> = {},
  ): Promise<Stripe.PaymentIntent | { id: string; client_secret: string }> {
    // 如果Stripe未初始化，抛出错误而不是返回模拟的支付意向
    if (!this.stripe) {
      this.logger.error('Stripe not initialized');
      throw new Error('Stripe payment service is not configured');
    }

    try {
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: Math.round(amount), // 直接使用美元金额，不进行额外转换
        currency,
        metadata,
        automatic_payment_methods: {
          enabled: true,
        },
      });

      this.logger.debug('Payment intent created', {
        id: paymentIntent.id,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
      });

      return paymentIntent;
    } catch (error) {
      this.logger.error('Failed to create payment intent', {
        error: error instanceof Error ? error.message : error,
        amount,
        currency,
      });
      throw error;
    }
  }

  /**
   * 创建模拟的支付意向
   */
  private createMockPaymentIntent(amount: number, metadata: Record<string, string> = {}): { id: string; client_secret: string } {
    const id = `pi_mock_${Date.now()}`;
    const clientSecret = `pi_mock_secret_${Date.now()}_secret_${Math.random().toString(36).substring(2, 15)}`;

    this.logger.debug('Created mock payment intent', { id, amount, metadata });

    return {
      id,
      client_secret: clientSecret,
    };
  }

  /**
   * 查询支付意向
   */
  async retrievePaymentIntent(paymentIntentId: string): Promise<Stripe.PaymentIntent | { id: string; client_secret: string }> {
    // 如果Stripe未初始化，返回模拟的支付意向
    if (!this.stripe) {
      this.logger.warn('Stripe not initialized, returning mock payment intent');
      return this.createMockPaymentIntent(0, { id: paymentIntentId });
    }

    try {
      return await this.stripe.paymentIntents.retrieve(paymentIntentId);
    } catch (error) {
      this.logger.error('Failed to retrieve payment intent', {
        error: error instanceof Error ? error.message : error,
        paymentIntentId,
      });
      // 如果发生错误，返回模拟的支付意向
      return this.createMockPaymentIntent(0, { id: paymentIntentId });
    }
  }

  /**
   * 处理Stripe Webhook事件
   */
  async handleWebhookEvent(
    signature: string,
    payload: Buffer | undefined,
  ): Promise<{ received: boolean }> {
    // 如果Stripe未初始化，返回模拟的响应
    if (!this.stripe) {
      this.logger.warn('Stripe not initialized, mocking webhook response');
      return { received: true };
    }

    const webhookSecret = this.configService.get<string>('STRIPE_WEBHOOK_SECRET');

    if (!webhookSecret) {
      this.logger.error('STRIPE_WEBHOOK_SECRET is not defined in environment variables');
      return { received: false };
    }

    if (!payload) {
      this.logger.error('Webhook payload is undefined');
      return { received: false };
    }

    try {
      const event = this.stripe.webhooks.constructEvent(
        payload,
        signature,
        webhookSecret,
      );

      this.logger.debug('Webhook event received', {
        type: event.type,
        id: event.id,
      });

      // 处理不同类型的事件
      switch (event.type) {
        case 'payment_intent.succeeded':
          await this.handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent);
          break;
        case 'payment_intent.payment_failed':
          await this.handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent);
          break;
        case 'charge.updated':
          const charge = event.data.object as Stripe.Charge;
          if (charge.payment_intent) {
            const paymentIntent = await this.stripe.paymentIntents.retrieve(charge.payment_intent as string);
            if (paymentIntent.status === 'succeeded') {
              await this.handlePaymentIntentSucceeded(paymentIntent);
            }
          }
          break;
        default:
          this.logger.debug(`Unhandled event type: ${event.type}`);
      }

      return { received: true };
    } catch (error) {
      this.logger.error('Error handling webhook event', {
        error: error instanceof Error ? error.message : error,
      });
      throw error;
    }
  }

  /**
   * 处理支付成功事件
   */
  private async handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent): Promise<void> {
    this.logger.debug('Payment intent succeeded', {
      id: paymentIntent.id,
      amount: paymentIntent.amount,
      metadata: paymentIntent.metadata,
    });

    // 更新订单状态为已支付
    if (paymentIntent.metadata.orderNo) {
      try {
        await this.orderService.handlePaymentSuccess(
          paymentIntent.metadata.orderNo,
          paymentIntent.id
        );
        this.logger.debug('Order status updated to PAID', {
          orderNo: paymentIntent.metadata.orderNo,
          paymentIntentId: paymentIntent.id
        });
      } catch (error) {
        this.logger.error('Failed to update order status', {
          error: error instanceof Error ? error.message : error,
          orderNo: paymentIntent.metadata.orderNo,
          paymentIntentId: paymentIntent.id
        });
      }
    }
  }

  /**
   * 处理支付失败事件
   */
  private async handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent): Promise<void> {
    this.logger.debug('Payment intent failed', {
      id: paymentIntent.id,
      amount: paymentIntent.amount,
      metadata: paymentIntent.metadata,
      error: paymentIntent.last_payment_error,
    });

    // 这里可以添加处理支付失败的逻辑
    // 例如：记录失败原因，通知用户等
  }
}
