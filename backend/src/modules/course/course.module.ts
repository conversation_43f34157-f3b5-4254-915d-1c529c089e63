import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Course } from './entities/course.entity';
import { CourseLesson } from './entities/course-lesson.entity';
import { UserCourse } from './entities/user-course.entity';
import { CourseService } from './services/course.service';
import { CourseController } from './controllers/course.controller';
import { UploadController } from './controllers/upload.controller';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';
import { CourseGeneratorService } from './services/course-generator.service';
import { CourseGeneratorController } from './controllers/course-generator.controller';
import { AiModule } from '../ai/ai.module';
import { Category } from './entities/category.entity';
import { CategoryService } from './services/category.service';
import { CategoryController } from './controllers/category.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Course,
      CourseLesson,
      UserCourse,
      Category,
    ]),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', '..', '..', 'uploads'),
      serveRoot: '/uploads',
    }),
    AiModule,
  ],
  controllers: [
    CourseController,
    CourseGeneratorController,
    UploadController,
    CategoryController,
  ],
  providers: [
    CourseService,
    CourseGeneratorService,
    CategoryService,
  ],
  exports: [CourseService],
})
export class CourseModule {}
