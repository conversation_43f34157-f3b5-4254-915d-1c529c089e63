import {
  Controller,
  Get,
  Post,
  Param,
  Query,
  Body,
  UseGuards,
  NotFoundException,
  Patch,
  Delete,
  ForbiddenException,
  BadRequestException,
  Put,
  Req,
  HttpException,
  HttpStatus,
  Inject,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../user/entities/user.entity';
import { CurrentUser } from '../../auth/decorators/current-user.decorator';
import { CourseService } from '../services/course.service';
import {
  CourseDto,
  CourseQueryDto,
  LessonDto,
  UpdateProgressDto,
  UpdateCourseDto,
} from '../dto/course.dto';
import { User } from '../../user/entities/user.entity';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>acheTTL } from '@nestjs/cache-manager';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { Request } from 'express';

// 扩展Cache类型以支持两种缓存清除方法
interface ExtendedCache extends Cache {
  delete?: (key: string) => Promise<void>;
}

// 缓存键常量
export const COURSE_CACHE_KEY = {
  LIST: 'courses:list',
  DETAIL: (id: string) => `courses:${id}`,
  USER_COURSES: (userId: string) => `courses:user:${userId}`,
  LESSONS: (courseId: string) => `courses:${courseId}:lessons`
};

// 缓存时间常量（秒）
export const COURSE_CACHE_TTL = {
  LIST: 60, // 1分钟
  DETAIL: 300, // 5分钟
  USER_COURSES: 60, // 1分钟
  LESSONS: 300 // 5分钟
};

// 扩展 Express 的 Request 类型
interface AuthenticatedRequest extends Request {
  user: User;
}

@ApiTags('课程')
@Controller('courses')
export class CourseController {
  constructor(
    private readonly courseService: CourseService,
    @Inject(CACHE_MANAGER) private cacheManager: ExtendedCache,
  ) {}

  @Get()
  @CacheKey(COURSE_CACHE_KEY.LIST)
  @CacheTTL(COURSE_CACHE_TTL.LIST)
  @ApiOperation({ summary: '获取课程列表' })
  @ApiResponse({ status: 200, description: '成功获取课程列表' })
  async findAll(@Query('includeUnpublished') includeUnpublished: boolean, @CurrentUser('id') userId?: string) {
    const courses = await this.courseService.findAll(includeUnpublished);
    
    if (userId) {
      // 如果用户已登录，查询购买状态和学习进度
      const coursesWithStatus = await Promise.all(
        courses.map(async (course) => {
          const isPurchased = await this.courseService.hasUserPurchased(userId, course.id);
          const progress = isPurchased ? await this.courseService.getUserProgress(userId, course.id) : 0;
          return {
            ...course,
            isPurchased,
            progress,
          };
        })
      );
      return coursesWithStatus;
    }

    return courses;
  }

  @Get('purchased')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '获取已购买的课程' })
  async getPurchasedCourses(@CurrentUser() user: User) {
    return this.courseService.getPurchasedCourses(user.id);
  }

  @Get(':id')
  @CacheKey(`courses:detail`)
  @CacheTTL(COURSE_CACHE_TTL.DETAIL)
  @ApiOperation({ summary: '获取课程详情' })
  @ApiResponse({ status: 200, description: '成功获取课程详情' })
  async findOne(@Param('id') id: string, @CurrentUser('id') userId?: string) {
    const course = await this.courseService.findOne(id);
    
    if (userId) {
      const isPurchased = await this.courseService.hasUserPurchased(userId, id);
      const progress = isPurchased ? await this.courseService.getUserProgress(userId, id) : 0;
      return {
        ...course,
        isPurchased,
        progress,
      };
    }

    return course;
  }

  @Get(':id/lessons')
  @UseGuards(JwtAuthGuard)
  @CacheKey(`courses:lessons`)
  @CacheTTL(COURSE_CACHE_TTL.LESSONS)
  @ApiOperation({ summary: '获取课程章节列表（带学习进度）' })
  @ApiResponse({ status: 200, description: '成功获取课程章节列表' })
  async findUserLessons(@Param('id') id: string, @CurrentUser() user: User) {
    // 管理员可以直接访问
    if (user.role === UserRole.ADMIN) {
      const lessons = await this.courseService.findLessons(id);
      return lessons;
    }

    // 普通用户需要先购买课程
    const isPurchased = await this.courseService.hasUserPurchased(user.id, id);
    if (!isPurchased) {
      throw new NotFoundException('请先购买课程');
    }

    const lessons = await this.courseService.findLessons(id);
    const userCourse = await this.courseService.getUserCourse(user.id, id);

    // 添加章节完成状态
    return lessons.map(lesson => ({
      ...lesson,
      isCompleted: userCourse.completedLessons.includes(lesson.id),
    }));
  }

  @Get(':id/public-lessons')
  @CacheKey(`courses:lessons:public`)
  @CacheTTL(COURSE_CACHE_TTL.LESSONS)
  @ApiOperation({ summary: '获取课程章节列表（公开）' })
  @ApiResponse({ status: 200, description: '成功获取课程章节列表' })
  async findPublicLessons(@Param('id') id: string) {
    const lessons = await this.courseService.findLessons(id);
    // 只返回基本信息，不包含学习进度
    return lessons.map(lesson => ({
      id: lesson.id,
      courseId: lesson.courseId,
      title: lesson.title,
      order: lesson.order,
      duration: lesson.duration,
      description: lesson.description
    }));
  }

  @Post(':id/lessons/:lessonId/progress')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '更新学习进度' })
  @ApiResponse({ status: 200 })
  async updateProgress(
    @Param('id') courseId: string,
    @Param('lessonId') lessonId: string,
    @CurrentUser('id') userId: string,
  ) {
    // 检查用户是否已购买课程
    const isPurchased = await this.courseService.hasUserPurchased(userId, courseId);
    if (!isPurchased) {
      throw new NotFoundException('请先购买课程');
    }

    await this.courseService.updateProgress(userId, courseId, lessonId);
    return { success: true };
  }

  @Get(':id/progress')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '获取学习进度' })
  @ApiResponse({ status: 200 })
  async getProgress(@Param('id') courseId: string, @CurrentUser('id') userId: string) {
    // 检查用户是否已购买课程
    const isPurchased = await this.courseService.hasUserPurchased(userId, courseId);
    if (!isPurchased) {
      throw new NotFoundException('请先购买课程');
    }

    const userCourse = await this.courseService.getUserCourse(userId, courseId);
    return {
      progress: userCourse.progress,
      completedLessons: userCourse.completedLessons,
      lastVisitedLessonId: userCourse.lastVisitedLessonId,
    };
  }

  @Get(':id/lessons/:lessonId/content')
  @UseGuards(JwtAuthGuard)
  async getLessonContent(
    @Param('id') courseId: string,
    @Param('lessonId') lessonId: string,
    @CurrentUser() user: User,
  ) {
    // 管理员可以直接访问
    if (user.role === UserRole.ADMIN) {
      const content = await this.courseService.getLessonContent(courseId, lessonId);
      return { content };
    }

    // 检查用户是否已购买课程
    const hasPurchased = await this.courseService.hasUserPurchased(user.id, courseId);
    if (!hasPurchased) {
      throw new ForbiddenException('请先购买课程');
    }

    const content = await this.courseService.getLessonContent(courseId, lessonId);
    return { content };
  }

  @Put(':id/lessons/:lessonId/content')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  async updateLessonContent(
    @Param('id') courseId: string,
    @Param('lessonId') lessonId: string,
    @Body() body: { content: string },
  ) {
    try {
      await this.courseService.updateLessonContent(courseId, lessonId, body.content);
      return { message: '更新成功' };
    } catch (error) {
      throw new BadRequestException('更新失败：' + error.message);
    }
  }

  // 管理员接口
  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  async create(@Body() courseData: any) {
    return this.courseService.create(courseData);
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: '更新课程' })
  @ApiResponse({ status: 200, description: '成功更新课程' })
  async update(@Param('id') id: string, @Body() updateCourseDto: UpdateCourseDto) {
    const result = await this.courseService.update(id, updateCourseDto);
    // 清除相关缓存
    try {
      // 兼容不同版本的cache-manager
      if (typeof this.cacheManager.del === 'function') {
        await this.cacheManager.del(COURSE_CACHE_KEY.LIST);
        await this.cacheManager.del(COURSE_CACHE_KEY.DETAIL(id));
      } else if (typeof this.cacheManager.delete === 'function') {
        await this.cacheManager.delete(COURSE_CACHE_KEY.LIST);
        await this.cacheManager.delete(COURSE_CACHE_KEY.DETAIL(id));
      } else {
        console.warn('缓存管理器不支持清除缓存的方法');
      }
    } catch (error) {
      console.error('清除缓存时出错:', error);
    }
    return result;
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: '删除课程' })
  @ApiResponse({ status: 200, description: '成功删除课程' })
  async remove(@Param('id') id: string) {
    const result = await this.courseService.delete(id);
    // 清除相关缓存
    try {
      // 兼容不同版本的cache-manager
      if (typeof this.cacheManager.del === 'function') {
        await this.cacheManager.del(COURSE_CACHE_KEY.LIST);
        await this.cacheManager.del(COURSE_CACHE_KEY.DETAIL(id));
        await this.cacheManager.del(COURSE_CACHE_KEY.LESSONS(id));
      } else if (typeof this.cacheManager.delete === 'function') {
        await this.cacheManager.delete(COURSE_CACHE_KEY.LIST);
        await this.cacheManager.delete(COURSE_CACHE_KEY.DETAIL(id));
        await this.cacheManager.delete(COURSE_CACHE_KEY.LESSONS(id));
      } else {
        console.warn('缓存管理器不支持清除缓存的方法');
      }
    } catch (error) {
      console.error('清除缓存时出错:', error);
    }
    return result;
  }

  @Post(':id/publish')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: '发布课程' })
  async publish(@Param('id') id: string) {
    return this.courseService.publish(id);
  }

  @Post(':id/unpublish')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: '取消发布课程' })
  async unpublish(@Param('id') id: string) {
    return this.courseService.unpublish(id);
  }

  @Post(':id/toggle-publish')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: '切换课程发布状态' })
  async togglePublish(@Param('id') id: string) {
    return this.courseService.togglePublish(id);
  }

  // 章节管理接口
  @Post(':id/lessons')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: '创建课程章节' })
  async createLesson(
    @Param('id') courseId: string,
    @Body() lessonData: LessonDto
  ) {
    return this.courseService.createLesson(courseId, lessonData);
  }

  @Patch(':id/lessons/:lessonId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: '更新课程章节' })
  async updateLesson(
    @Param('id') courseId: string,
    @Param('lessonId') lessonId: string,
    @Body() lessonData: Partial<LessonDto>
  ) {
    return this.courseService.updateLesson(courseId, lessonId, lessonData);
  }

  @Delete(':id/lessons/:lessonId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: '删除课程章节' })
  async deleteLesson(
    @Param('id') courseId: string,
    @Param('lessonId') lessonId: string
  ) {
    return this.courseService.deleteLesson(courseId, lessonId);
  }

  @Post(':id/lessons/reorder')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: '重新排序课程章节' })
  async reorderLessons(
    @Param('id') courseId: string,
    @Body() data: { lessonIds: string[] }
  ) {
    await this.courseService.reorderLessons(courseId, data.lessonIds);
    return { message: '重新排序成功' };
  }

  /**
   * 购买课程
   */
  @Post(':id/purchase')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '购买课程' })
  @ApiResponse({ status: 200, description: '成功购买课程' })
  async purchase(@Param('id') id: string, @Req() req: AuthenticatedRequest) {
    try {
      // 购买逻辑
      const result = await this.courseService.purchaseCourse(id, req.user.id);
      
      // 清除缓存
      try {
        // 兼容不同版本的cache-manager
        if (typeof this.cacheManager.del === 'function') {
          await this.cacheManager.del(COURSE_CACHE_KEY.LIST);
          await this.cacheManager.del(COURSE_CACHE_KEY.DETAIL(id));
          await this.cacheManager.del(COURSE_CACHE_KEY.USER_COURSES(req.user.id));
        } else if (typeof this.cacheManager.delete === 'function') {
          await this.cacheManager.delete(COURSE_CACHE_KEY.LIST);
          await this.cacheManager.delete(COURSE_CACHE_KEY.DETAIL(id));
          await this.cacheManager.delete(COURSE_CACHE_KEY.USER_COURSES(req.user.id));
        } else {
          console.warn('缓存管理器不支持清除缓存的方法');
        }
      } catch (error) {
        console.error('清除缓存时出错:', error);
      }
      
      return result;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        error.message || '购买课程失败',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 加入免费课程
   */
  @Post('free-enroll/:id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '加入免费课程' })
  @ApiResponse({ status: 200, description: '成功加入免费课程' })
  async freeEnroll(@Param('id') id: string, @CurrentUser() user: User) {
    try {
      // 1. 验证课程存在且价格为0
      const course = await this.courseService.findOne(id);
      
      if (!course) {
        throw new NotFoundException(`课程不存在`);
      }
      
      console.log('后端 - 课程价格:', course.price, typeof course.price);
      
      // 使用Number()确保价格比较正确
      if (Number(course.price) !== 0) {
        throw new ForbiddenException(`该课程不是免费课程`);
      }
      
      // 2. 检查是否已加入
      const hasPurchased = await this.courseService.hasUserPurchased(user.id, id);
      if (hasPurchased) {
        return { message: '您已加入此课程', courseId: id };
      }
      
      // 3. 授予课程访问权限
      await this.courseService.grantCourseAccess(user.id, id);
      
      // 4. 清除相关缓存
      try {
        if (typeof this.cacheManager.del === 'function') {
          await this.cacheManager.del(COURSE_CACHE_KEY.USER_COURSES(user.id));
        } else if (typeof this.cacheManager.delete === 'function') {
          await this.cacheManager.delete(COURSE_CACHE_KEY.USER_COURSES(user.id));
        }
      } catch (error) {
        console.error('清除缓存时出错:', error);
      }
      
      return { 
        message: '成功加入课程', 
        courseId: id 
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        error.message || '加入课程失败',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
} 