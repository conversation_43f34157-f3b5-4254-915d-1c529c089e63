import { Injectable, Logger } from '@nestjs/common';
import { AiService } from '../../ai/services/ai.service';
import { PromptManager } from '../../ai/services/prompt/prompt-manager';
import { BaseAiService } from '../../ai/services/base/base-ai.service';
import { RegenerateSectionDto } from '../dto/course-generation.dto';

export interface CourseGenerationParams {
  title: string;           // 课程标题
  rawMaterial: string;     // 原始素材
  difficulty: 'beginner' | 'intermediate' | 'advanced';  // 难度级别
  targetAudience: string;  // 目标受众
  sectionCount?: number;   // 小节数量
  interactionCount?: number;// 每节交互轮数
  includeExercises?: boolean;  // 是否包含练习
  tone?: 'formal' | 'casual';  // 语气风格
}

@Injectable()
export class CourseGeneratorService {
  private readonly logger = new Logger(CourseGeneratorService.name);

  constructor(
    private readonly aiService: AiService,
    private readonly promptManager: PromptManager,
  ) {}

  async generateCourse(params: CourseGenerationParams): Promise<string> {
    try {
      this.logger.log('Starting course generation:', {
        title: params.title,
        difficulty: params.difficulty,
        targetAudience: params.targetAudience,
        sectionCount: params.sectionCount,
        timestamp: new Date().toISOString(),
      });

      const service = await this.aiService.getDefaultService();
      
      // 构建完整的提示词
      const prompt = `请根据以下要求生成一个完整的交互式课程内容：

课程信息：
- 标题：${params.title}
- 难度级别：${params.difficulty}
- 目标受众：${params.targetAudience}
- 语气风格：${params.tone || 'casual'}
- 小节数量：${params.sectionCount || 3}
- 每节交互轮数：${params.interactionCount || 20}
- 是否包含练习：${params.includeExercises ? '是' : '否'}

原始素材：
${params.rawMaterial}

要求：
1. 内容要循序渐进，适合${params.targetAudience}学习
2. 使用[系统]和[讲师]的对话形式展示内容
3. 在重要概念后添加互动练习
4. 每个小节都要有明确的学习目标和总结
5. 语气要${params.tone || 'casual'}且专业

内容结构：
1. 课程导入：介绍课程目标和重要性
2. 主要内容：分为${params.sectionCount || 3}个小节
3. 课程总结：回顾要点并提供后续学习建议

请按照以下格式生成内容：

# ${params.title}

## 课程导入
[系统]: ...
[讲师]: ...

## 第一部分：[标题]
[系统]: ...
[讲师]: ...
[practice:chat mode="interactive"]
练习内容...
[/practice]

## 第二部分：[标题]
...以此类推

## 课程总结
[系统]: ...
[讲师]: ...`;

      this.logger.log('Sending chat request with prompt:', {
        promptLength: prompt.length,
        systemPromptType: 'courseGenerator',
      });

      // 一次性生成所有内容
      const response = await service.chat({
        messages: [
          { role: 'system', content: this.promptManager.getSystemPrompt('courseGenerator') },
          { role: 'user', content: prompt }
        ]
      });

      this.logger.log('Successfully generated course content:', {
        contentLength: response.length,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      this.logger.error('Failed to generate course:', {
        errorType: error.constructor.name,
        errorMessage: error.message,
        params: {
          title: params.title,
          difficulty: params.difficulty,
          targetAudience: params.targetAudience,
        },
        timestamp: new Date().toISOString(),
        stack: error.stack,
      });

      // 根据错误类型返回更具体的错误信息
      if (error.message.includes('timeout') || error.message.includes('超时')) {
        throw new Error('生成课程内容超时，请减少内容量或稍后重试');
      }
      
      if (error.message.includes('rate limit') || error.message.includes('频率')) {
        throw new Error('系统繁忙，请稍后重试');
      }

      if (error.message.includes('API key') || error.message.includes('密钥')) {
        throw new Error('AI 服务配置错误，请联系管理员');
      }

      throw new Error(`生成课程失败：${error.message}`);
    }
  }

  private async generateOutline(params: CourseGenerationParams): Promise<string> {
    const service = await this.aiService.getDefaultService();
    const response = await service.chat({
      messages: [
        { role: 'system', content: this.promptManager.getSystemPrompt('courseGenerator') },
        { role: 'user', content: this.promptManager.generateOutlinePrompt(params) }
      ]
    });

    return response;
  }

  private async generateIntroduction(params: CourseGenerationParams, outline: string): Promise<string> {
    const service = await this.aiService.getDefaultService();
    const response = await service.chat({
      messages: [
        { role: 'system', content: this.promptManager.getSystemPrompt('courseGenerator') },
        { role: 'user', content: this.promptManager.generateIntroductionPrompt(params, outline) }
      ]
    });

    return response;
  }

  private async generateMainContent(params: CourseGenerationParams, outline: string): Promise<string> {
    const sections = outline.split('\n').filter(line => line.startsWith('###')).map(line => line.replace('### ', ''));
    let mainContent = '';

    for (const section of sections) {
      const sectionContent = await this.generateSection(params, section, outline);
      mainContent += '\n\n' + sectionContent;
    }

    return mainContent;
  }

  private async generateSection(params: CourseGenerationParams, sectionTitle: string, outline: string): Promise<string> {
    const service = await this.aiService.getDefaultService();
    const response = await service.chat({
      messages: [
        { role: 'system', content: this.promptManager.getSystemPrompt('courseGenerator') },
        { role: 'user', content: this.promptManager.generateSectionPrompt(params, sectionTitle) }
      ]
    });

    return response;
  }

  private async generateSummary(params: CourseGenerationParams, outline: string): Promise<string> {
    const service = await this.aiService.getDefaultService();
    const response = await service.chat({
      messages: [
        { role: 'system', content: this.promptManager.getSystemPrompt('courseGenerator') },
        { role: 'user', content: this.promptManager.generateSummaryPrompt(params, outline) }
      ]
    });

    return response;
  }

  private combineContent(introduction: string, mainContent: string, summary: string): string {
    return `${introduction}\n\n${mainContent}\n\n${summary}`;
  }

  async regenerateSection(params: RegenerateSectionDto): Promise<string> {
    try {
      this.logger.log('Starting section regeneration:', {
        sectionTitle: params.sectionTitle,
        difficulty: params.difficulty,
        timestamp: new Date().toISOString(),
      });

      const service = await this.aiService.getDefaultService();
      const sectionPrompt = this.promptManager.generateSectionPrompt({
        difficulty: params.difficulty,
        interactionCount: params.interactionCount,
        includeExercises: params.includeExercises,
      } as CourseGenerationParams, params.sectionTitle);

      this.logger.log('Sending chat request for section regeneration:', {
        promptLength: sectionPrompt.length,
        systemPromptType: 'courseGenerator',
      });

      const response = await service.chat({
        messages: [
          { role: 'system', content: this.promptManager.getSystemPrompt('courseGenerator') },
          { role: 'user', content: sectionPrompt }
        ]
      });

      this.logger.log('Successfully regenerated section content:', {
        contentLength: response.length,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      this.logger.error('Failed to regenerate section:', {
        errorType: error.constructor.name,
        errorMessage: error.message,
        params: {
          sectionTitle: params.sectionTitle,
          difficulty: params.difficulty,
        },
        timestamp: new Date().toISOString(),
        stack: error.stack,
      });

      // 根据错误类型返回更具体的错误信息
      if (error.message.includes('timeout') || error.message.includes('超时')) {
        throw new Error('重新生成小节内容超时，请减少内容量或稍后重试');
      }
      
      if (error.message.includes('rate limit') || error.message.includes('频率')) {
        throw new Error('系统繁忙，请稍后重试');
      }

      if (error.message.includes('API key') || error.message.includes('密钥')) {
        throw new Error('AI 服务配置错误，请联系管理员');
      }

      throw new Error(`重新生成小节失败：${error.message}`);
    }
  }
} 