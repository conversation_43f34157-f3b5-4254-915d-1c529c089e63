import { Controller, Get, UseGuards, Request, Post, Body, HttpException, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { SubscriptionService } from '../services/subscription.service';
import { Request as ExpressRequest } from 'express';

@ApiTags('订阅')
@Controller('subscription')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class SubscriptionController {
  constructor(private readonly subscriptionService: SubscriptionService) {}

  @Get('current')
  @ApiOperation({ summary: '获取当前订阅信息' })
  async getCurrentSubscription(@Request() req: ExpressRequest) {
    const userId = (req.user as any).id;
    return this.subscriptionService.getCurrentSubscription(userId);
  }

  @Get('history')
  @ApiOperation({ summary: '获取订阅历史' })
  async getSubscriptionHistory(@Request() req: ExpressRequest) {
    const userId = (req.user as any).id;
    return this.subscriptionService.getSubscriptionHistory(userId);
  }

  // 仅在开发环境可用的测试接口
  @Post('test/set-time')
  async setTestTime(@Body() body: { time: string }) {
    if (process.env.NODE_ENV === 'production') {
      throw new HttpException('测试接口在生产环境不可用', HttpStatus.FORBIDDEN);
    }
    this.subscriptionService.setTestTime(new Date(body.time));
    return { message: '测试时间设置成功' };
  }

  @Post('test/clear-time')
  async clearTestTime() {
    if (process.env.NODE_ENV === 'production') {
      throw new HttpException('测试接口在生产环境不可用', HttpStatus.FORBIDDEN);
    }
    this.subscriptionService.clearTestTime();
    return { message: '测试时间已清除' };
  }
} 