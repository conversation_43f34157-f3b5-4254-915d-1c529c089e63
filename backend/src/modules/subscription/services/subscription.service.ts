import { Injectable, Logger, Inject, forwardRef, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager, MoreThan, LessThanOrEqual } from 'typeorm';
import { Subscription } from '../entities/subscription.entity';

@Injectable()
export class SubscriptionService {
  private readonly logger = new Logger(SubscriptionService.name);

  constructor(
    @InjectRepository(Subscription)
    private readonly subscriptionRepository: Repository<Subscription>,
  ) {}

  // 添加用于测试的时间
  private testNow?: Date;

  /**
   * 设置测试时间（仅用于测试）
   */
  setTestTime(date: Date) {
    this.testNow = date;
    this.logger.debug('Set test time', { testTime: date });
  }

  /**
   * 清除测试时间
   */
  clearTestTime() {
    this.testNow = undefined;
    this.logger.debug('Cleared test time');
  }

  /**
   * 获取当前时间
   * 如果设置了测试时间，返回测试时间
   * 否则返回真实时间
   */
  private getNow(): Date {
    return this.testNow || new Date();
  }

  /**
   * 计算新订阅的起止时间
   * 如果有现有订阅，新订阅从现有订阅的结束时间开始计算
   * 如果没有现有订阅，从当前时间开始计算
   */
  private async calculateNewSubscriptionDates(
    userId: string,
    durationDays: number,
    entityManager?: EntityManager
  ): Promise<{ startDate: Date; endDate: Date }> {
    const repo = entityManager?.getRepository(Subscription) || this.subscriptionRepository;
    const now = this.getNow();
    
    // 查找用户当前有效的订阅（按结束时间降序排序）
    const currentSubscriptions = await repo.find({
      where: {
        userId,
        endDate: MoreThan(now),
        isActive: true
      },
      order: { endDate: 'DESC' }
    });

    this.logger.debug(`Found ${currentSubscriptions.length} active subscriptions for user ${userId}`);

    if (currentSubscriptions.length > 0) {
      // 如果有有效订阅，找出最晚的结束时间
      const latestEndDate = currentSubscriptions[0].endDate;
      const startDate = latestEndDate;
      const endDate = new Date(startDate.getTime() + durationDays * 24 * 60 * 60 * 1000);
      
      this.logger.debug('Calculated subscription dates', {
        userId,
        existingEndDate: latestEndDate,
        newStartDate: startDate,
        newEndDate: endDate
      });
      
      return { startDate, endDate };
    } else {
      // 如果没有有效订阅，从当前时间开始计算
      const endDate = new Date(now.getTime() + durationDays * 24 * 60 * 60 * 1000);
      
      this.logger.debug('Calculated subscription dates (no existing subscription)', {
        userId,
        startDate: now,
        endDate
      });
      
      return { startDate: now, endDate };
    }
  }

  /**
   * 检查未来订阅
   * 如果已经存在未来订阅，则抛出异常
   */
  private async checkFutureSubscriptions(
    userId: string,
    entityManager?: EntityManager
  ): Promise<void> {
    const repo = entityManager?.getRepository(Subscription) || this.subscriptionRepository;
    const now = this.getNow();

    const futureSubscriptions = await repo.find({
      where: {
        userId,
        startDate: MoreThan(now),
        isActive: true,
      },
    });

    if (futureSubscriptions.length > 0) {
      this.logger.warn(`User ${userId} already has future subscription`, {
        existingSubscriptionId: futureSubscriptions[0].id,
        startDate: futureSubscriptions[0].startDate,
        endDate: futureSubscriptions[0].endDate,
      });
      
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          error: `您已有一个从 ${this.formatDate(futureSubscriptions[0].startDate)} 开始生效的订阅，暂时无法创建新订阅。如需帮助，请联系客服。`,
          code: 'FUTURE_SUBSCRIPTION_EXISTS'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  private formatDate(date: Date): string {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  }

  /**
   * 创建订阅
   */
  async createSubscription(
    userId: string,
    params: {
      planName: string;
      price: number;
      duration: number;
      aiCallLimit: number;
    },
    entityManager?: EntityManager,
  ): Promise<Subscription> {
    const repo = entityManager?.getRepository(Subscription) || this.subscriptionRepository;

    try {
      // 1. 计算新订阅的起止时间
      const { startDate, endDate } = await this.calculateNewSubscriptionDates(
        userId,
        params.duration,
        entityManager
      );

      // 2. 如果是未来订阅，检查是否已存在未来订阅
      const now = this.getNow();
      if (startDate > now) {
        await this.checkFutureSubscriptions(userId, entityManager);
      }

      // 3. 创建新订阅（移除了停用当前订阅的步骤）
      const subscription = repo.create({
        userId,
        planName: params.planName,
        price: params.price,
        startDate,
        endDate,
        aiCallLimit: params.aiCallLimit,
        aiCallUsed: 0,
        isActive: true
      });

      this.logger.debug('Creating subscription', { 
        userId, 
        planName: params.planName,
        startDate,
        endDate
      });

      const savedSubscription = await repo.save(subscription);
      this.logger.debug('Subscription created', { 
        subscriptionId: savedSubscription.id,
        startDate: savedSubscription.startDate,
        endDate: savedSubscription.endDate
      });

      return savedSubscription;
    } catch (error) {
      this.logger.error('Failed to create subscription', {
        error: error instanceof Error ? error.message : error,
        userId,
        planName: params.planName
      });
      throw error;
    }
  }

  /**
   * 获取用户当前订阅
   * 返回优先级：
   * 1. 当前生效的订阅
   * 2. 未来即将生效的订阅
   */
  async getCurrentSubscription(userId: string, entityManager?: EntityManager): Promise<Subscription | null> {
    const repo = entityManager ? entityManager.getRepository(Subscription) : this.subscriptionRepository;
    
    return await repo.findOne({
      where: {
        userId,
        isActive: true,
      },
      order: {
        endDate: 'DESC',
      },
    });
  }

  /**
   * 获取用户订阅历史
   */
  async getSubscriptionHistory(userId: string): Promise<{ items: Subscription[]; total: number }> {
    const subscriptions = await this.subscriptionRepository.find({
      where: {
        userId,
      },
      order: {
        createdAt: 'DESC',
      },
    });

    return {
      items: subscriptions,
      total: subscriptions.length,
    };
  }

  /**
   * 更新 AI 调用次数
   */
  async updateAiCallUsed(subscriptionId: string, used: number): Promise<void> {
    try {
      await this.subscriptionRepository.update(
        { id: subscriptionId },
        { aiCallUsed: used },
      );
    } catch (error) {
      this.logger.error('Failed to update AI call used count', {
        error: error instanceof Error ? error.message : error,
        subscriptionId,
        used,
      });
      throw error;
    }
  }

  /**
   * 更新订阅
   */
  async updateSubscription(id: string, data: Partial<Subscription>) {
    await this.subscriptionRepository.update(id, data);
    return await this.subscriptionRepository.findOne({ where: { id } });
  }

  /**
   * 使当前有效的订阅失效
   * 注意：此方法仅在特殊情况下使用，如订阅被取消或管理员手动停用订阅时。
   * 在正常的订阅流程中（如创建新订阅、兑换礼品码等），不应该停用当前生效的订阅。
   */
  private async deactivateActiveSubscriptions(
    userId: string,
    entityManager?: EntityManager
  ): Promise<void> {
    const repo = entityManager?.getRepository(Subscription) || this.subscriptionRepository;
    const now = this.getNow();

    try {
      // 查找当前生效的订阅（开始时间小于等于现在，结束时间大于现在）
      const activeSubscriptions = await repo.find({
        where: {
          userId,
          startDate: LessThanOrEqual(now),
          endDate: MoreThan(now),
          isActive: true
        }
      });

      if (activeSubscriptions.length === 0) {
        this.logger.debug(`No active subscriptions found for user ${userId}`);
        return;
      }

      // 逐个更新并保存订阅
      for (const subscription of activeSubscriptions) {
        subscription.isActive = false;
        subscription.updatedAt = now;
        await repo.save(subscription);
        
        this.logger.debug(`Deactivated subscription`, {
          subscriptionId: subscription.id,
          userId,
          oldEndDate: subscription.endDate
        });
      }

      this.logger.debug(`Successfully deactivated all subscriptions`, {
        userId,
        count: activeSubscriptions.length,
        subscriptionIds: activeSubscriptions.map(s => s.id)
      });
    } catch (error) {
      this.logger.error('Failed to deactivate subscriptions', {
        error: error instanceof Error ? error.message : error,
        userId
      });
      throw error;
    }
  }

  async deactivateSubscription(id: string) {
    return await this.subscriptionRepository.update(id, {
      isActive: false,
    });
  }
}