import { <PERSON><PERSON>ty, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from '../../user/entities/user.entity';

@Entity('subscriptions')
export class Subscription {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'userid', type: 'uuid', nullable: false })
  userId: string;

  @ManyToOne(() => User, user => user.subscriptions)
  @JoinColumn({ name: 'userid' })
  user: User;

  @Column({ name: 'planname', nullable: false })
  planName: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  price: number;

  @Column({ name: 'startdate', type: 'timestamp' })
  startDate: Date;

  @Column({ name: 'enddate', type: 'timestamp' })
  endDate: Date;

  @Column({ name: 'aicalllimit', type: 'integer' })
  aiCallLimit: number;

  @Column({ name: 'aicallused', type: 'integer', default: 0 })
  aiCallUsed: number;

  @Column({ name: 'isactive', type: 'boolean', default: true })
  isActive: boolean;

  @CreateDateColumn({ name: 'createdat', type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updatedat', type: 'timestamp' })
  updatedAt: Date;
} 