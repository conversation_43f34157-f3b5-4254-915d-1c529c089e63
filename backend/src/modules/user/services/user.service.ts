import { Injectable, NotFoundException, ConflictException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsSelect } from 'typeorm';
import { User, UserRole } from '../entities/user.entity';
import * as bcrypt from 'bcryptjs';
import { USER_MESSAGES, USER_CONSTANTS } from '../constants/user.constants';
import { IUserProfile, IChangePassword } from '../interfaces/user.interface';
import { CreateUserDto, UpdateUserDto } from '../dto/user.dto';

@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async findAll(options?: { select?: (keyof User)[] }): Promise<User[]> {
    return this.userRepository.find({
      ...(options?.select ? { select: options.select.reduce((acc, key) => ({ ...acc, [key]: true }), {}) } : {}),
    });
  }

  async findById(id: string): Promise<User> {
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    return user;
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.userRepository.findOne({ where: { email } });
  }

  async create(createUserDto: CreateUserDto): Promise<User> {
    const user = this.userRepository.create(createUserDto);
    return this.userRepository.save(user);
  }

  private validateRole(role: string): boolean {
    return Object.values(UserRole).includes(role as UserRole);
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    const user = await this.findById(id);
    Object.assign(user, updateUserDto);
    return this.userRepository.save(user);
  }

  async validatePassword(user: User, password: string): Promise<boolean> {
    return bcrypt.compare(password, user.password);
  }

  private async hashPassword(password: string): Promise<string> {
    console.log('Hashing password:', { 
      originalLength: password.length,
      original: password 
    });
    
    if (password.length < USER_CONSTANTS.PASSWORD_MIN_LENGTH) {
      throw new ConflictException(USER_MESSAGES.PASSWORD_TOO_SHORT);
    }
    
    const hashedPassword = await bcrypt.hash(password, 10);
    console.log('Password hashed:', { 
      hashedLength: hashedPassword.length,
      hashed: hashedPassword 
    });
    
    return hashedPassword;
  }

  async delete(id: string): Promise<void> {
    const user = await this.findById(id);
    await this.userRepository.remove(user);
  }

  async updateProfile(id: string, data: IUserProfile): Promise<User> {
    const user = await this.findById(id);
    if (data.name && data.name.length < USER_CONSTANTS.NAME_MIN_LENGTH) {
      throw new ConflictException(USER_MESSAGES.NAME_TOO_SHORT);
    }
    Object.assign(user, data);
    return this.userRepository.save(user);
  }

  async updatePassword(id: string, oldPassword: string, newPassword: string): Promise<void> {
    const user = await this.findById(id);
    const isValid = await this.validatePassword(user, oldPassword);
    
    if (!isValid) {
      throw new ConflictException(USER_MESSAGES.INVALID_PASSWORD);
    }

    if (newPassword.length < USER_CONSTANTS.PASSWORD_MIN_LENGTH) {
      throw new ConflictException(USER_MESSAGES.PASSWORD_TOO_SHORT);
    }

    user.password = await this.hashPassword(newPassword);
    await this.userRepository.save(user);
  }

  async updateRole(id: string, role: UserRole): Promise<User> {
    this.logger.log('Updating user role', {
      userId: id,
      newRole: role,
      timestamp: new Date().toISOString()
    });

    const user = await this.findById(id);
    const oldRole = user.role;

    if (!this.isValidRoleTransition(oldRole, role)) {
      const error = `Invalid role transition from ${oldRole} to ${role}`;
      this.logger.error(error, {
        userId: id,
        timestamp: new Date().toISOString()
      });
      throw new BadRequestException(error);
    }

    try {
      await this.logRoleChange(user, oldRole, role);

      user.role = role;
      
      const updatedUser = await this.userRepository.save(user);
      
      this.logger.log('User role updated successfully', {
        userId: id,
        oldRole,
        newRole: role,
        timestamp: new Date().toISOString()
      });

      return updatedUser;
    } catch (error) {
      this.logger.error('Failed to update user role', {
        error,
        userId: id,
        oldRole,
        newRole: role,
        timestamp: new Date().toISOString()
      });
      throw error;
    }
  }

  private isValidRoleTransition(fromRole: UserRole, toRole: UserRole): boolean {
    return true;
  }

  private async logRoleChange(user: User, oldRole: string, newRole: string): Promise<void> {
    try {
      this.logger.log('Role change logged', {
        userId: user.id,
        email: user.email,
        oldRole,
        newRole,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      this.logger.error('Failed to log role change', {
        error,
        userId: user.id,
        timestamp: new Date().toISOString()
      });
    }
  }

  async getUserHistory(userId: string): Promise<any[]> {
    try {
      this.logger.log('Fetching user history', {
        userId,
        timestamp: new Date().toISOString()
      });
      
      return [];
    } catch (error) {
      this.logger.error('Failed to fetch user history', {
        error,
        userId,
        timestamp: new Date().toISOString()
      });
      throw error;
    }
  }
} 