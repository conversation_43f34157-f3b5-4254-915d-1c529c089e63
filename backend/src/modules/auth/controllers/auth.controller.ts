import {
  Controller,
  Post,
  Body,
  Get,
  UseGuards,
  UnauthorizedException,
  Request as NestRequest,
  HttpCode,
  HttpStatus,
  <PERSON>s,
  BadRequestException,
} from '@nestjs/common';
import { Request, Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiResponse,
  ApiBody
} from '@nestjs/swagger';
import { AuthService } from '../services/auth.service';
import { LoginDto, RegisterDto } from '../dto/auth.dto';
import { VerificationService } from '../../verification/verification.service';
import { SendVerificationCodeDto } from '../../verification/dto/send-verification-code.dto';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { LocalAuthGuard } from '../guards/local-auth.guard';
import { UserRole } from '../../user/entities/user.entity';
import { AUTH_ERRORS } from '../constants/auth.constants';
import { CurrentUser } from '../decorators/current-user.decorator';
import { User } from '../../user/entities/user.entity';
import { ENV_CONFIG } from '@/config/environment';

interface RequestWithUser extends Request {
  user: User;
}

@ApiTags('认证')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly verificationService: VerificationService,
  ) {}

  @Post('send-verification-code')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '发送邮箱验证码' })
  @ApiBody({ type: SendVerificationCodeDto })
  @ApiResponse({ status: 200, description: '验证码发送成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 500, description: '服务器错误' })
  async sendVerificationCode(@Body() dto: SendVerificationCodeDto) {
    const result = await this.verificationService.sendVerificationCode(dto.email);

    if (!result) {
      throw new BadRequestException('验证码发送失败，请稍后重试');
    }

    return { success: true, message: '验证码发送成功' };
  }

  @Post('register')
  @ApiOperation({ summary: '用户注册' })
  @ApiBody({ type: RegisterDto })
  @ApiResponse({
    status: 201,
    description: '注册成功',
    schema: {
      properties: {
        token: { type: 'string' },
        user: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            name: { type: 'string' },
            role: { type: 'string', enum: ['user', 'admin'] },
            avatar: { type: 'string', nullable: true },
            isActive: { type: 'boolean' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 409, description: '邮箱已被注册' })
  async register(
    @Body() registerDto: RegisterDto,
    @Res({ passthrough: true }) response: Response
  ) {
    return this.authService.register(registerDto, response);
  }

  @UseGuards(LocalAuthGuard)
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '用户登录' })
  @ApiBody({ type: LoginDto })
  @ApiResponse({
    status: 200,
    description: '登录成功',
    schema: {
      properties: {
        user: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            name: { type: 'string' },
            role: { type: 'string', enum: ['user', 'admin'] },
            avatar: { type: 'string', nullable: true },
            isActive: { type: 'boolean' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: '邮箱或密码错误' })
  async login(
    @NestRequest() req: RequestWithUser,
    @Res({ passthrough: true }) response: Response
  ) {
    return this.authService.login(req.user, response);
  }

  @UseGuards(LocalAuthGuard)
  @Post('admin/login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '管理员登录' })
  @ApiBody({ type: LoginDto })
  @ApiResponse({
    status: 200,
    description: '登录成功',
    schema: {
      properties: {
        user: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            name: { type: 'string' },
            role: { type: 'string', enum: ['user', 'admin'] },
            avatar: { type: 'string', nullable: true },
            isActive: { type: 'boolean' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: '邮箱或密码错误' })
  @ApiResponse({ status: 403, description: '非管理员账号' })
  async adminLogin(
    @NestRequest() req: RequestWithUser,
    @Res({ passthrough: true }) response: Response
  ) {
    const result = await this.authService.login(req.user, response);
    if (result.user.role !== UserRole.ADMIN) {
      throw new UnauthorizedException('该账号没有管理员权限');
    }
    return result;
  }

  @UseGuards(JwtAuthGuard)
  @Get('me')
  @ApiOperation({ summary: '获取当前用户信息' })
  @ApiBearerAuth()
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      properties: {
        id: { type: 'string' },
        email: { type: 'string' },
        name: { type: 'string' },
        role: { type: 'string', enum: ['user', 'admin'] },
        avatar: { type: 'string', nullable: true },
        isActive: { type: 'boolean' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' }
      }
    }
  })
  @ApiResponse({ status: 401, description: '未登录或 Token 已过期' })
  async getCurrentUser(@CurrentUser() user: User) {
    const validatedUser = await this.authService.validateUser(user.id);
    return this.authService.formatUserResponse(validatedUser);
  }

  @UseGuards(JwtAuthGuard)
  @Post('refresh')
  @ApiOperation({ summary: '刷新Token' })
  @ApiBearerAuth()
  @ApiResponse({
    status: 200,
    description: '刷新成功',
    schema: {
      properties: {
        token: { type: 'string' },
        user: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            name: { type: 'string' },
            role: { type: 'string', enum: ['user', 'admin'] },
            avatar: { type: 'string', nullable: true },
            isActive: { type: 'boolean' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: '未登录或 Token 已过期' })
  async refreshToken(
    @CurrentUser() user: User,
    @Res({ passthrough: true }) response: Response
  ) {
    return this.authService.refreshToken(user.id, response);
  }

  @UseGuards(JwtAuthGuard)
  @Post('logout')
  @ApiOperation({ summary: '用户登出' })
  @ApiBearerAuth()
  @ApiResponse({ status: 200, description: '登出成功' })
  @ApiResponse({ status: 401, description: '未登录或 Token 已过期' })
  async logout(@Res({ passthrough: true }) response: Response) {
    const envType = ENV_CONFIG.isDevelopment ? 'development' : 'production';
    const cookieConfig = {
      ...ENV_CONFIG.cookie.common,
      ...ENV_CONFIG.cookie[envType],
    };

    // 清理所有认证相关的 cookie
    response.clearCookie('auth_token', cookieConfig);
    response.clearCookie('auth.token', cookieConfig);

    console.log('[AuthController] 用户登出，清理 Cookie', {
      environment: process.env.NODE_ENV,
      isDevelopment: ENV_CONFIG.isDevelopment,
      cookieConfig,
      timestamp: new Date().toISOString(),
    });

    return { success: true };
  }
}