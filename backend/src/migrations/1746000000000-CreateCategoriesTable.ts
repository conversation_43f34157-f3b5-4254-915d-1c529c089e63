import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateCategoriesTable1746000000000 implements MigrationInterface {
    name = 'CreateCategoriesTable1746000000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "categories" (
                "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                "name" varchar NOT NULL UNIQUE,
                "description" text,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now()
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "courses" ADD "category_id" uuid;
        `);
        await queryRunner.query(`
            ALTER TABLE "courses" ADD CONSTRAINT "fk_courses_category" FOREIGN KEY ("category_id") REFERENCES "categories"("id") ON DELETE SET NULL;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "courses" DROP CONSTRAINT "fk_courses_category"`);
        await queryRunner.query(`ALTER TABLE "courses" DROP COLUMN "category_id"`);
        await queryRunner.query(`DROP TABLE IF EXISTS "categories"`);
    }
} 