# App
PORT=3001
NODE_ENV=production

# Database
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=ailessonsuser
DB_PASSWORD=ailessons123456
DB_DATABASE=ailessons
DB_SYNCHRONIZE=false
DB_LOGGING=false

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis123

# Cache
CACHE_TTL=300
CACHE_MAX=1000

# JWT
JWT_SECRET=ai_learning_jwt_secret_2024
JWT_EXPIRATION=3600
JWT_REFRESH_SECRET=ai_learning_jwt_refresh_secret_2024
JWT_REFRESH_EXPIRES_IN=30d

# CORS
CORS_ORIGIN=http://***************

# Admin
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=PPOf3vcxvcLGB0etr9wA1w==
ADMIN_NAME=System Admin

# 日志配置
LOG_LEVEL=warn
LOG_FILE_PATH=/var/log/ai-learning/app.log

# PostgreSQL environment variables
POSTGRES_USER=ai_learning_user
POSTGRES_PASSWORD=ailearning123456
POSTGRES_DB=ailessons


#strip 設置
STRIPE_SECRET_KEY=***********************************************************************************************************
STRIPE_PUBLISHABLE_KEY=pk_live_51RHh9FKV3OLSrdJCL6WCLS9KGmdfCyTsxneEnUY4DIzlQpdJbcZegPDp0uLrzvaSTc7Hfjy4aboIoFeUVbCuVRiU00AyEZjMAy
STRIPE_WEBHOOK_SECRET=whsec_QWLhYHVCIJeqoG2th9Yxs1iOOMPdd0jy