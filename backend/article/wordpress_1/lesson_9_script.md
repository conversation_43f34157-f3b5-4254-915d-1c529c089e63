 ## 课程导入  
[系统]: 欢迎来到WordPress独立站搭建课程！今天我们将继续用3小时带你从零开始创建一个专业的跨境电商B2C零售网站首页。准备好了吗？  
[讲师]: 嗨！我们先来生成一下首页的主题图片，这个主题图片应该是根据你的网站的销售内容来设定的。


[practice:image mode="create" size="1920x600"]
我们将要生成一个首页1920*600的首图，替代模版上的图片，在这里写入这张图片生成的要求和说明，生成完成后请点击右键保存图片...
[/practice]





---  

## 第一部分：准备工作与主题安装  
[系统]: 首先我们需要做好基础准备，就像盖房子要先打地基一样。  
[讲师]: 没错！第一步很简单：  
1. 确保你已经购买了域名和主机（推荐SiteGround或Bluehost）  
2. 登录你的WordPress后台（通常是你的域名+/wp-admin）  

[practice:chat mode="interactive"]  
练习：你能找到WordPress后台登录页面吗？试着输入：  
你的域名 + /wp-admin  
然后告诉我你看到了什么颜色的登录界面？  
[/practice]  

[讲师]: 现在我们来安装主题：  
• 左侧菜单点击"外观"→"主题"  
• 点击"添加新主题"  
• 搜索"Kadence"或"Astra"（推荐跨境电商使用）  
• 点击"安装"然后"启用"  

[系统]: 看到这个蓝色按钮了吗？这就是WordPress神奇的开始！  

---  

## 第二部分：用块编辑器搭建首页框架  
[系统]: 现在进入最有趣的部分 - 像搭积木一样建首页！  
[讲师]: 点击"页面"→"添加新页面"，给它起名"Home"。你会看到这个界面：  

[图片示意：WordPress块编辑器界面]  

我们来添加关键模块：  
1. 顶部横幅（Cover区块）：放主推产品图  
2. 产品分类展示（Gallery区块）  
3. 促销倒计时（Countdown区块插件）  

[practice:chat mode="interactive"]  
练习：试着添加一个"Cover"区块：  
1. 点击"+"按钮  
2. 搜索"cover"  
3. 上传一张图片  
你能看到实时预览效果吗？  
[/practice]  

[讲师]: 小技巧：按Ctrl+S可以随时保存，别担心出错！  

---  

## 第三部分：关键元素优化与发布  
[系统]: 最后我们来让首页变得更专业！  
[讲师]: 三个必改项：  
1. 网站标识：外观→自定义→添加Logo  
2. 导航菜单：外观→菜单→创建"Main Menu"  
3. 联系信息：在页脚添加WhatsApp图标  

[系统]: 记住：跨境电商站必须有的元素：  
✓ 货币切换器  
✓ 语言选择器  
✓ Trust badges（信任标识）  

[practice:chat mode="interactive"]  
练习：给你的首页添加一个"按钮"区块，文字写"Shop Now"，然后试试改变按钮颜色。找到这个选项了吗？  
[/practice]  

---  

## 课程总结  
[系统]: 太棒了！你已经完成了：  
✓ 安装专业主题  
✓ 搭建首页框架  
✓ 添加关键元素  

[讲师]: 接下来建议：  
1. 去PageSpeed Insights测试网站速度  
2. 安装WooCommerce插件准备添加产品  
3. 明天我们继续设计产品页！  

需要任何帮助随时问我 - 你现在的首页已经超过80%的初学者啦！ 🎉