# 创建产品信息

## 课程导入
[系统]: 欢迎来到WordPress跨境电商独立站课程！今天我们要学习如何使用WooCommerce创建专业的产品页面。准备好了吗？
[讲师]: 嘿朋友们！我是你们的讲师。在接下来的3小时里，我会带你从零开始创建完整的产品页面。学完这节课，你就能轻松添加产品、设置价格和上传图片啦！请按点击继续

## 第一部分：认识WooCommerce产品后台
[系统]: 首先让我们熟悉一下WooCommerce的产品管理界面。


[讲师]: 看到左侧菜单栏了吗？点击"产品"→"添加新产品"，这就是我们的主战场！

[media:video title="405_1744529166.mp4"]
/uploads/videos/c5672bfd-23cc-4e63-ba35-102c23d4f737.mp4



这里分为几个关键区域：
1. 标题和描述区
2. 产品数据面板
3. 产品图片区
4. 发布按钮



[系统]: 很好！现在我们来详细看看"产品数据"面板。
[讲师]: 这里有6个重要选项：
- 简单产品(最常用)
- 可变产品(有不同规格)
- 分组产品
- 外部/关联产品
- 虚拟产品
- 可下载产品

[media:image title="截屏2025-04-13 下午3.29.39.png"]
/uploads/images/fd662008-b97d-43df-b424-b9403a7e256d.png



## 第二部分：添加基础产品信息
[系统]: 现在我们来添加第一个产品！
[讲师]: 跟着我做：
1. 输入产品名称："优质棉质T恤"
2. 在描述区写："100%纯棉，舒适透气"
3. 价格填：19.99
4. 促销价(可选)：15.99

[practice:chat mode="interactive"]
练习：试着创建一个新产品，名字叫"防水登山包"，价格设为89.99，促销价69.99
[/practice]

[系统]: 太棒了！别忘了产品图片也很重要。
[讲师]: 点击"添加产品图片"，上传至少一张主图。建议尺寸：800x800像素。可以拖拽调整图片顺序，第一张就是主图哦！

## 第三部分：完善产品细节
[系统]: 让我们把产品信息做得更专业！
[讲师]: 在产品数据面板中：
1. 库存选项：勾选"管理库存"，输入SKU(比如T-SHIRT-001)和库存数量
2. 运输选项：设置重量(0.3kg)和尺寸
3. 属性：添加颜色、尺寸等

[practice:chat mode="interactive"]
练习：为你刚才创建的登山包添加这些属性：
- 颜色：黑色/军绿色
- 容量：30L/50L
- 材质：尼龙
[/practice]

[系统]: 最后一步：分类和标签！
[讲师]: 在右侧边栏：
1. 创建产品分类："服装→男装→T恤"
2. 添加标签："棉质"、"夏季"、"基础款"
这样顾客更容易找到你的产品！

## 课程总结
[系统]: 恭喜你完成了产品创建课程！让我们回顾一下重点：
1. 找到并熟悉产品添加界面
2. 填写基础产品信息
3. 设置价格和库存
4. 添加图片和属性
5. 分类和标签管理

[讲师]: 干得漂亮！现在你已经掌握了创建产品的基本技能。建议你多练习创建不同类型的产品，下节课我们会学习如何设置运费和支付方式。有什么问题随时问我哦！