# 第二章：基础使用技巧

[系统]: 欢迎来到第二章！在这一章中，我们将学习 DeepSeek Coder 的基础使用技巧。

[讲师]: 让我们先了解一下 DeepSeek Coder 的界面功能。

[系统]: DeepSeek Coder 的界面主要包含以下几个部分：
1. 输入区：用于输入你的问题或需求
2. 对话历史：显示之前的对话内容
3. 工具栏：包含各种功能按钮
4. 设置面板：可以调整 AI 的行为

[讲师]: 接下来，我们来学习如何构建有效的提示词。一个好的提示词应该：
1. 清晰明确
2. 提供足够的上下文
3. 说明具体需求
4. 包含必要的约束

[系统]: 让我们通过一个练习来掌握提示词的编写。

[practice:chat mode="interactive"]
请编写一个提示词，要求 DeepSeek Coder 帮你优化一个简单的 Python 函数：
```python
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)
```
要求：
1. 提高执行效率
2. 减少内存使用
3. 保持代码可读性
4. 添加适当的类型提示
[/practice]

[讲师]: 很好！你已经学会了如何编写清晰的提示词。接下来我们将学习如何管理对话上下文。

[系统]: 在下一节中，我们将探讨如何有效地管理对话上下文，以获得更好的交互体验。
