# 零基础入门：AI大模型本地化部署从理论到实战 - 各种微调方法介绍和对比

## 课程导入
[系统]: 你好，欢迎来到本课程！今天我们将学习各种微调方法，帮助初学者更好地理解如何优化大模型。这些知识将有助于你在实际项目中选择最合适的方法。
[讲师]: 嗯，那我们开始吧！首先要了解的是，为什么需要微调？通俗地讲，微调是指在已有的大模型基础上，通过少量参数的调整来适应新的任务。下面是多种微调方式的列表

[media:image title="截屏2025-02-18 上午10.50.50.png"]
/uploads/images/48b1d905-fefa-4c7c-8c17-4994cfd92d0c.png



## 第一部分：全参数微调简介
[系统]: 讲师，我听说全参数微调是最常见的方法之一，可以简单介绍一下吗？
[讲师]: 当然！全参数微调就是在大模型上对所有参数进行微调，这种方法可以很好地适应新的任务，但需要大量的计算资源和数据。

[practice:chat mode="interactive"]
请问全参数微调的主要优点和缺点是什么？
```
优点：对新任务的适应性强。
缺点：需要大量的计算资源和数据。
```
[/practice]

## 第二部分：LoRA及其优缺点
[系统]: 之前我们讨论过LoRA，可以快速回顾一下吗？
[讲师]: LoRA是一种参数效率高的微调方法，它通过在既有参数上添加少量的额外参数来实现微调。LoRA的优点是参数量少、计算成本低，缺点是可能无法很好地适应复杂的任务。

[practice:chat mode="interactive"]
LoRA的核心思想是什么？LoRA的主要应用场景是什么？
```
核心思想：通过在既有参数上添加少量的额外参数来实现微调。
主要应用场景：计算资源有限或任务较简单时。
```
[/practice]

## 第三部分：Adapter的介绍
[系统]: 讲师，Adapter是另一种微调方法，可以简单介绍一下吗？
[讲师]: Adapter也是一种参数效率高的微调方法，它通过在模型的每个子层上添加一个轻量级的adapter module来实现微调。Adapter的优点是参数量少、计算成本低，缺点是可能需要修改模型结构。

[practice:chat mode="interactive"]
请问Adapter的主要优点和缺点是什么？Adapter的核心原理是什么？
```
主要优点：参数量少、计算成本低。
主要缺点：可能需要修改模型结构。
核心原理：在模型的每个子层上添加一个轻量级的adapter module。
```
[/practice]

## 第四部分：Prefix-tuning和Prompt-tuning的区别
[系统]: 讲师，Prefix-tuning和Prompt-tuning有什么区别？它们各自的优缺点是什么？
[讲师]: Prefix-tuning和Prompt-tuning都是以提示（prompt）为核心思想的微调方法，但它们的实现不同。Prefix-tuning在模型输入序列的开头添加一些额外的令牌，而Prompt-tuning则在输入序列中插入提示。Prefix-tuning的优点是参数量少，缺点是可能需要修改模型结构；Prompt-tuning的优点是灵活性高，缺点是参数量可能相对较多。

[practice:chat mode="interactive"]
请问Prefix-tuning和Prompt-tuning的主要区别是什么？它们各自的主要应用场景是什么？
```
主要区别：Prefix-tuning在输入序列开头添加额外令牌，而Prompt-tuning在输入序列中插入提示。
Prefix-tuning主要应用场景：计算资源有限或任务较简单时。
Prompt-tuning主要应用场景：需要更高灵活性时。
```
[/practice]

## 第五部分：其他微调方法及选择建议
[系统]: 除了前面介绍的方法外，还有哪些微调方法？它们各自的优缺点是什么？
[讲师]: 还有一些其他微调方法，比如BitFit、RAG等。BitFit只调整模型的 bias 和 LayerNorm 参数，参数量极少；RAG则是通过自适应地选择微调参数来提高效率。

[practice:chat mode="interactive"]
请问BitFit和RAG的主要优点和缺点是什么？
```
BitFit优点：参数量极少。缺点：适应性可能有限。
RAG优点：自适应选择微调参数提高效率。缺点：可能需要更多的计算资源。
```
[/practice]

## 课程总结
[系统]: 讲师，今天我们学习了很多微调方法，你可以给我们一些选择建议吗？
[讲师]: 当然！选择微调方法时，主要考虑三个因素：数据量、计算资源和任务复杂度。如果数据量和计算资源充足，可以选择全参数微调；如果数据量有限或计算资源紧张，可以选择参数效率高的微调方法，比如LoRA、Adapter、Prefix-tuning、Prompt-tuning等。在实际项目中，还可以结合多种方法。

选择建议
数据规模：

百万级 → 全参数微调
万级 → LoRA/Adapter
百级 → BitFit/P-tuning
计算资源：

单卡 → LoRA/P-tuning
多卡集群 → 全参数微调
任务类型：

生成任务 → Prefix-tuning
分类任务 → LoRA/Adapter
低延迟要求 → BitFit