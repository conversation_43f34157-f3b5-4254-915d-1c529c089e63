docker pull ollama/ollama

docker run -d -v D:\ollama:/root/.ollama -p 11434:11434 --name ollama ollama/ollama

docker exec -it ollama ollama run llama2

[media:image title="4383295765bba1482ab831cc5d1dc52a.png"]
/uploads/images/098a8d5d-923d-4ba0-83cd-5e8541109e57.png


docker run -p 3000:3000 -e DEFAULT_MODEL=llama2:latest -e OLLAMA_HOST=http://IP地址:11434 ghcr.io/ivanfioravanti/chatbot-ollama:main

[media:image title="********************************.png"]
/uploads/images/134e92bd-0c24-48d9-9a96-2e805fecfd88.png


