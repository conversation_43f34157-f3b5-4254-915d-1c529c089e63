# Dependencies
node_modules
**/node_modules
**/.pnpm
.pnp
.pnp.js
.pnpm-store
.pnpm-debug.log

# Testing
**/coverage
.nyc_output

# Production
**/build
**/dist
frontend/.next/
frontend/out/
frontend/build/
frontend/dist/
backend/dist/
backend/build/

# Misc
**/.DS_Store
**/*.pem
.DS_Store
Thumbs.db
._*
.Spotlight-V100
.Trashes
ehthumbs.db

# Debug
**/npm-debug.log*
**/yarn-debug.log*
**/yarn-error.log*
**/pnpm-debug.log*



# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*~

# Cache
.cache/
.next/
.nuxt/
.vuepress/dist
.serverless/
.fusebox/
.dynamodb/
.eslintcache
.stylelintcache
.temp/

# Docker related
Dockerfile
docker-compose*.yml
.dockerignore
nginx/
.docker/

# Lock files
**/package-lock.json
**/yarn.lock
**/pnpm-lock.yaml

# Deployment files
DEPLOYMENT_GUIDE.md
deploy.sh
nginx*.conf

# Build artifacts
**/dist/
**/dist.tar.gz
**/*.tar.gz
backend-dist.tar.gz

# Certificates and keys
backend/certs/*.pem
backend/certs/*.key
backend/certs/*.crt
backend/certs/*.p12
*.pem
*.key
*.crt
*.csr
*.der
*.p12
*.pfx
!backend/certs/.gitkeep

# Upload directories
backend/uploads/*
!backend/uploads/.gitkeep
uploads/

# Temporary files
*.log
*.tmp
.tmp/
temp/
tmp/
*.temp
